1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.smsbridge"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- SMS permissions -->
12    <uses-permission android:name="android.permission.READ_SMS" />
12-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.RECEIVE_SMS" />
13-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:7:5-70
13-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:7:22-67
14    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:8:5-75
14-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:8:22-72
15
16    <!-- Network permissions -->
17    <uses-permission android:name="android.permission.INTERNET" />
17-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:11:5-67
17-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:11:22-64
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:12:5-79
18-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:12:22-76
19
20    <!-- Background work permissions -->
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:15:5-68
21-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:15:22-65
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:16:5-77
22-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:16:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
23-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:17:5-87
23-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:17:22-84
24    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
24-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
24-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
25
26    <permission
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
27        android:name="com.example.smsbridge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.example.smsbridge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
31
32    <application
32-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:19:5-68:19
33        android:name="com.example.smsbridge.SMSBridgeApplication"
33-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:20:9-45
34        android:allowBackup="true"
34-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:21:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
36        android:dataExtractionRules="@xml/data_extraction_rules"
36-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:22:9-65
37        android:debuggable="true"
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:23:9-54
40        android:icon="@mipmap/ic_launcher"
40-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:24:9-43
41        android:label="@string/app_name"
41-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:25:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:26:9-54
43        android:supportsRtl="true"
43-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:27:9-35
44        android:testOnly="true"
45        android:theme="@style/Theme.SMSBridge" >
45-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:28:9-47
46        <activity
46-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:29:9-38:20
47            android:name="com.example.smsbridge.MainActivity"
47-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:30:13-41
48            android:exported="true"
48-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:31:13-36
49            android:label="@string/app_name"
49-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:32:13-45
50            android:theme="@style/Theme.SMSBridge" >
50-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:33:13-51
51            <intent-filter>
51-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:34:13-37:29
52                <action android:name="android.intent.action.MAIN" />
52-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:35:17-69
52-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:35:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:36:17-77
54-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:36:27-74
55            </intent-filter>
56        </activity>
57
58        <!-- SMS Broadcast Receiver -->
59        <receiver
59-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:41:9-48:20
60            android:name="com.example.smsbridge.receiver.SmsReceiver"
60-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:42:13-49
61            android:enabled="true"
61-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:43:13-35
62            android:exported="true" >
62-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:44:13-36
63            <intent-filter android:priority="1000" >
63-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:45:13-47:29
63-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:45:28-51
64                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
64-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:46:17-82
64-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:46:25-79
65            </intent-filter>
66        </receiver>
67
68        <!-- SMS Sync Service -->
69        <service
69-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:51:9-55:56
70            android:name="com.example.smsbridge.service.SmsSyncService"
70-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:52:13-51
71            android:enabled="true"
71-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:53:13-35
72            android:exported="false"
72-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:54:13-37
73            android:foregroundServiceType="dataSync" />
73-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:55:13-53
74
75        <!-- Work Manager for background sync -->
76        <provider
77            android:name="androidx.startup.InitializationProvider"
77-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:59:13-67
78            android:authorities="com.example.smsbridge.androidx-startup"
78-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:60:13-68
79            android:exported="false" >
79-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:61:13-37
80            <meta-data
80-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:63:13-65:52
81                android:name="androidx.work.WorkManagerInitializer"
81-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:64:17-68
82                android:value="androidx.startup" />
82-->C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:65:17-49
83            <meta-data
83-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
84                android:name="io.github.jan.supabase.gotrue.SupabaseInitializer"
84-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
85                android:value="androidx.startup" />
85-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
86            <meta-data
86-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.emoji2.text.EmojiCompatInitializer"
87-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
88                android:value="androidx.startup" />
88-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
89            <meta-data
89-->[androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:29:13-31:52
90                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
90-->[androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:30:17-78
91                android:value="androidx.startup" />
91-->[androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:31:17-49
92            <meta-data
92-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
93                android:name="com.russhwolf.settings.SettingsInitializer"
93-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
94                android:value="androidx.startup" />
94-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
95            <meta-data
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
97                android:value="androidx.startup" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
98        </provider>
99
100        <service
100-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
101            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
102            android:directBootAware="false"
102-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
103            android:enabled="@bool/enable_system_alarm_service_default"
103-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
104            android:exported="false" />
104-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
105        <service
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
106            android:name="androidx.work.impl.background.systemjob.SystemJobService"
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
107            android:directBootAware="false"
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
108            android:enabled="@bool/enable_system_job_service_default"
108-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
109            android:exported="true"
109-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
110            android:permission="android.permission.BIND_JOB_SERVICE" />
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
111        <service
111-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
112            android:name="androidx.work.impl.foreground.SystemForegroundService"
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
113            android:directBootAware="false"
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
114            android:enabled="@bool/enable_system_foreground_service_default"
114-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
115            android:exported="false" />
115-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
116
117        <receiver
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
118            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
118-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
119            android:directBootAware="false"
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
120            android:enabled="true"
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
121            android:exported="false" />
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
122        <receiver
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
123            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
124            android:directBootAware="false"
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
125            android:enabled="false"
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
126            android:exported="false" >
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
127            <intent-filter>
127-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
128                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
128-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
128-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
129                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
130            </intent-filter>
131        </receiver>
132        <receiver
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
133            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
134            android:directBootAware="false"
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
135            android:enabled="false"
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
136            android:exported="false" >
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
137            <intent-filter>
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
138                <action android:name="android.intent.action.BATTERY_OKAY" />
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
139                <action android:name="android.intent.action.BATTERY_LOW" />
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
140            </intent-filter>
141        </receiver>
142        <receiver
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
143            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
144            android:directBootAware="false"
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
145            android:enabled="false"
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
146            android:exported="false" >
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
147            <intent-filter>
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
148                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
149                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
150            </intent-filter>
151        </receiver>
152        <receiver
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
153            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
155            android:enabled="false"
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
156            android:exported="false" >
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
157            <intent-filter>
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
158                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
159            </intent-filter>
160        </receiver>
161        <receiver
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
162            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
164            android:enabled="false"
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
165            android:exported="false" >
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
166            <intent-filter>
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
167                <action android:name="android.intent.action.BOOT_COMPLETED" />
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
168                <action android:name="android.intent.action.TIME_SET" />
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
169                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
173            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
175            android:enabled="@bool/enable_system_alarm_service_default"
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
178                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
182            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
184            android:enabled="true"
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
185            android:exported="true"
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
186            android:permission="android.permission.DUMP" >
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
187            <intent-filter>
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
188                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
189            </intent-filter>
190        </receiver>
191
192        <activity
192-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
193            android:name="androidx.compose.ui.tooling.PreviewActivity"
193-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
194            android:exported="true" />
194-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
195        <activity
195-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
196            android:name="androidx.activity.ComponentActivity"
196-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
197            android:exported="true" />
197-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
198
199        <service
199-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
200            android:name="androidx.room.MultiInstanceInvalidationService"
200-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
201            android:directBootAware="true"
201-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
202            android:exported="false" />
202-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
203
204        <receiver
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
205            android:name="androidx.profileinstaller.ProfileInstallReceiver"
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
206            android:directBootAware="false"
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
207            android:enabled="true"
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
208            android:exported="true"
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
209            android:permission="android.permission.DUMP" >
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
211                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
212            </intent-filter>
213            <intent-filter>
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
214                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
215            </intent-filter>
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
217                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
218            </intent-filter>
219            <intent-filter>
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
220                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
221            </intent-filter>
222        </receiver>
223    </application>
224
225</manifest>
