package com.example.smsbridge.ui.screen.settings

import android.provider.Settings
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.smsbridge.data.preferences.PreferencesManager
import com.example.smsbridge.data.remote.SupabaseConfig
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

data class SettingsUiState(
    val supabaseUrl: String = "",
    val supabaseAnonKey: String = "",
    val isSupabaseConfigured: Boolean = false,
    val isAutoSyncEnabled: Boolean = true,
    val isRealTimeSyncEnabled: Boolean = true,
    val isSyncOnWifiOnly: Boolean = false,
    val syncFrequencyHours: Long = 24L,
    val deviceId: String? = null,
    val message: String? = null,
    val isError: Boolean = false
)

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val preferencesManager: PreferencesManager,
    private val supabaseConfig: SupabaseConfig
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    init {
        observePreferences()
        generateDeviceIdIfNeeded()
        checkSupabaseConfiguration()
    }
    
    private fun observePreferences() {
        viewModelScope.launch {
            combine(
                preferencesManager.supabaseUrl,
                preferencesManager.supabaseAnonKey,
                preferencesManager.isAutoSyncEnabled,
                preferencesManager.isRealTimeSyncEnabled,
                preferencesManager.isSyncOnWifiOnly,
                preferencesManager.syncFrequencyHours,
                preferencesManager.deviceId
            ) { url, key, autoSync, realTimeSync, wifiOnly, frequency, deviceId ->
                _uiState.value.copy(
                    supabaseUrl = url ?: "",
                    supabaseAnonKey = key ?: "",
                    isAutoSyncEnabled = autoSync,
                    isRealTimeSyncEnabled = realTimeSync,
                    isSyncOnWifiOnly = wifiOnly,
                    syncFrequencyHours = frequency,
                    deviceId = deviceId
                )
            }.collect { newState ->
                _uiState.value = newState
            }
        }
    }
    
    private fun generateDeviceIdIfNeeded() {
        viewModelScope.launch {
            val currentDeviceId = preferencesManager.deviceId.first()
            if (currentDeviceId == null) {
                val newDeviceId = "device_${UUID.randomUUID().toString().take(8)}"
                preferencesManager.setDeviceId(newDeviceId)
            }
        }
    }
    
    private fun checkSupabaseConfiguration() {
        val isConfigured = supabaseConfig.isConfigured()
        _uiState.value = _uiState.value.copy(isSupabaseConfigured = isConfigured)
    }
    
    fun updateSupabaseUrl(url: String) {
        _uiState.value = _uiState.value.copy(supabaseUrl = url)
    }
    
    fun updateSupabaseAnonKey(key: String) {
        _uiState.value = _uiState.value.copy(supabaseAnonKey = key)
    }
    
    fun saveSupabaseConfig() {
        viewModelScope.launch {
            try {
                val url = _uiState.value.supabaseUrl.trim()
                val key = _uiState.value.supabaseAnonKey.trim()
                
                if (url.isBlank() || key.isBlank()) {
                    showMessage("Please fill in both URL and API key", isError = true)
                    return@launch
                }
                
                if (!url.startsWith("https://")) {
                    showMessage("Supabase URL must start with https://", isError = true)
                    return@launch
                }
                
                preferencesManager.setSupabaseConfig(url, key)
                checkSupabaseConfiguration()
                showMessage("Supabase configuration saved successfully!", isError = false)
                
            } catch (e: Exception) {
                showMessage("Failed to save configuration: ${e.message}", isError = true)
            }
        }
    }
    
    fun toggleAutoSync(enabled: Boolean) {
        viewModelScope.launch {
            preferencesManager.setAutoSyncEnabled(enabled)
            if (!enabled) {
                // If auto sync is disabled, also disable real-time sync
                preferencesManager.setRealTimeSyncEnabled(false)
            }
        }
    }
    
    fun toggleRealTimeSync(enabled: Boolean) {
        viewModelScope.launch {
            preferencesManager.setRealTimeSyncEnabled(enabled)
        }
    }
    
    fun toggleSyncOnWifiOnly(wifiOnly: Boolean) {
        viewModelScope.launch {
            preferencesManager.setSyncOnWifiOnly(wifiOnly)
        }
    }
    
    fun updateSyncFrequency(hours: Long) {
        viewModelScope.launch {
            preferencesManager.setSyncFrequencyHours(hours)
        }
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null, isError = false)
    }
    
    private fun showMessage(message: String, isError: Boolean) {
        _uiState.value = _uiState.value.copy(message = message, isError = isError)
    }
}
