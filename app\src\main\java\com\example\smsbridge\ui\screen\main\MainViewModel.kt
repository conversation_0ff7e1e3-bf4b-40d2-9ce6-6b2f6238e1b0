package com.example.smsbridge.ui.screen.main

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.workDataOf
import com.example.smsbridge.data.local.SmsContentProvider
import com.example.smsbridge.data.model.SyncStats
import com.example.smsbridge.data.preferences.PreferencesManager
import com.example.smsbridge.data.remote.SupabaseConfig
import com.example.smsbridge.worker.SmsSyncWorker
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

data class MainUiState(
    val syncStats: SyncStats = SyncStats(),
    val isSyncing: Boolean = false,
    val lastSyncTime: String? = null,
    val isSupabaseConfigured: Boolean = false,
    val errorMessage: String? = null
)

@HiltViewModel
class MainViewModel @Inject constructor(
    private val smsContentProvider: SmsContentProvider,
    private val preferencesManager: PreferencesManager,
    private val supabaseConfig: SupabaseConfig,
    private val workManager: WorkManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    private val dateFormatter = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
    
    init {
        observePreferences()
        loadSyncStats()
        checkSupabaseConfiguration()
    }
    
    private fun observePreferences() {
        viewModelScope.launch {
            preferencesManager.lastSyncTimestamp.collect { timestamp ->
                val lastSyncTime = if (timestamp > 0) {
                    dateFormatter.format(Date(timestamp))
                } else {
                    null
                }
                
                _uiState.value = _uiState.value.copy(lastSyncTime = lastSyncTime)
            }
        }
    }
    
    private fun loadSyncStats() {
        viewModelScope.launch {
            try {
                val totalMessages = smsContentProvider.getSmsMessageCount()
                
                // For now, we'll assume all messages are pending since we don't have
                // a local database to track sync status
                val syncStats = SyncStats(
                    totalMessages = totalMessages,
                    syncedMessages = 0, // This would come from local database
                    pendingMessages = totalMessages,
                    failedMessages = 0
                )
                
                _uiState.value = _uiState.value.copy(syncStats = syncStats)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to load SMS statistics: ${e.message}"
                )
            }
        }
    }
    
    private fun checkSupabaseConfiguration() {
        val isConfigured = supabaseConfig.isConfigured()
        _uiState.value = _uiState.value.copy(isSupabaseConfigured = isConfigured)
    }
    
    fun startManualSync() {
        if (_uiState.value.isSyncing || !_uiState.value.isSupabaseConfigured) {
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSyncing = true, errorMessage = null)
            
            try {
                val lastSyncTimestamp = preferencesManager.lastSyncTimestamp.first()
                
                val workData = workDataOf(
                    SmsSyncWorker.KEY_SYNC_TYPE to SmsSyncWorker.SYNC_TYPE_INCREMENTAL,
                    SmsSyncWorker.KEY_LAST_SYNC_TIMESTAMP to lastSyncTimestamp
                )
                
                val syncWorkRequest = OneTimeWorkRequestBuilder<SmsSyncWorker>()
                    .setInputData(workData)
                    .addTag(SmsSyncWorker.WORK_TAG_INCREMENTAL_SYNC)
                    .build()
                
                workManager.enqueue(syncWorkRequest)
                
                // Update last sync timestamp
                preferencesManager.setLastSyncTimestamp(System.currentTimeMillis())
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to start sync: ${e.message}"
                )
            } finally {
                _uiState.value = _uiState.value.copy(isSyncing = false)
            }
        }
    }
    
    fun startFullSync() {
        if (_uiState.value.isSyncing || !_uiState.value.isSupabaseConfigured) {
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSyncing = true, errorMessage = null)
            
            try {
                val workData = workDataOf(
                    SmsSyncWorker.KEY_SYNC_TYPE to SmsSyncWorker.SYNC_TYPE_FULL
                )
                
                val syncWorkRequest = OneTimeWorkRequestBuilder<SmsSyncWorker>()
                    .setInputData(workData)
                    .addTag(SmsSyncWorker.WORK_TAG_FULL_SYNC)
                    .build()
                
                workManager.enqueue(syncWorkRequest)
                
                // Update last sync timestamp
                preferencesManager.setLastSyncTimestamp(System.currentTimeMillis())
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to start full sync: ${e.message}"
                )
            } finally {
                _uiState.value = _uiState.value.copy(isSyncing = false)
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
}
