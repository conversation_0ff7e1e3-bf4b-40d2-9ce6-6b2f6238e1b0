package com.example.smsbridge

import com.example.smsbridge.data.model.SmsMessage
import com.example.smsbridge.data.model.SmsType
import com.example.smsbridge.data.model.SyncStatus
import com.example.smsbridge.data.model.toDto
import com.example.smsbridge.data.model.toDomain
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for SMS message data models
 */
class SmsMessageTest {

    @Test
    fun smsMessage_creation_isCorrect() {
        val message = SmsMessage(
            id = "test-id",
            androidId = 12345L,
            phoneNumber = "+1234567890",
            content = "Test message",
            timestamp = 1640995200000L,
            type = SmsType.INBOX,
            isRead = false,
            threadId = 1L,
            deviceId = "device-123",
            syncStatus = SyncStatus.PENDING
        )

        assertEquals("test-id", message.id)
        assertEquals(12345L, message.androidId)
        assertEquals("+1234567890", message.phoneNumber)
        assertEquals("Test message", message.content)
        assertEquals(1640995200000L, message.timestamp)
        assertEquals(SmsType.INBOX, message.type)
        assertEquals(false, message.isRead)
        assertEquals(1L, message.threadId)
        assertEquals("device-123", message.deviceId)
        assertEquals(SyncStatus.PENDING, message.syncStatus)
    }

    @Test
    fun smsType_fromValue_returnsCorrectType() {
        assertEquals(SmsType.INBOX, SmsType.fromValue(1))
        assertEquals(SmsType.SENT, SmsType.fromValue(2))
        assertEquals(SmsType.DRAFT, SmsType.fromValue(3))
        assertEquals(SmsType.OUTBOX, SmsType.fromValue(4))
        assertEquals(SmsType.FAILED, SmsType.fromValue(5))
        assertEquals(SmsType.QUEUED, SmsType.fromValue(6))
        
        // Test unknown value defaults to INBOX
        assertEquals(SmsType.INBOX, SmsType.fromValue(999))
    }

    @Test
    fun smsMessage_toDto_convertsCorrectly() {
        val message = SmsMessage(
            id = "test-id",
            androidId = 12345L,
            phoneNumber = "+1234567890",
            content = "Test message",
            timestamp = 1640995200000L,
            type = SmsType.SENT,
            isRead = true,
            threadId = 1L,
            deviceId = "device-123",
            syncStatus = SyncStatus.SYNCED
        )

        val dto = message.toDto()

        assertEquals("test-id", dto.id)
        assertEquals(12345L, dto.android_id)
        assertEquals("+1234567890", dto.phone_number)
        assertEquals("Test message", dto.content)
        assertEquals(1640995200000L, dto.timestamp)
        assertEquals("SENT", dto.type)
        assertEquals(true, dto.is_read)
        assertEquals(1L, dto.thread_id)
        assertEquals("device-123", dto.device_id)
        assertEquals("SYNCED", dto.sync_status)
    }

    @Test
    fun smsMessageDto_toDomain_convertsCorrectly() {
        val dto = com.example.smsbridge.data.model.SmsMessageDto(
            id = "test-id",
            android_id = 12345L,
            phone_number = "+1234567890",
            content = "Test message",
            timestamp = 1640995200000L,
            type = "INBOX",
            is_read = false,
            thread_id = 1L,
            device_id = "device-123",
            sync_status = "PENDING"
        )

        val message = dto.toDomain()

        assertEquals("test-id", message.id)
        assertEquals(12345L, message.androidId)
        assertEquals("+1234567890", message.phoneNumber)
        assertEquals("Test message", message.content)
        assertEquals(1640995200000L, message.timestamp)
        assertEquals(SmsType.INBOX, message.type)
        assertEquals(false, message.isRead)
        assertEquals(1L, message.threadId)
        assertEquals("device-123", message.deviceId)
        assertEquals(SyncStatus.PENDING, message.syncStatus)
    }

    @Test
    fun smsMessage_roundTripConversion_preservesData() {
        val originalMessage = SmsMessage(
            id = "test-id",
            androidId = 12345L,
            phoneNumber = "+1234567890",
            content = "Test message",
            timestamp = 1640995200000L,
            type = SmsType.DRAFT,
            isRead = true,
            threadId = 1L,
            deviceId = "device-123",
            syncStatus = SyncStatus.FAILED
        )

        val convertedMessage = originalMessage.toDto().toDomain()

        assertEquals(originalMessage.id, convertedMessage.id)
        assertEquals(originalMessage.androidId, convertedMessage.androidId)
        assertEquals(originalMessage.phoneNumber, convertedMessage.phoneNumber)
        assertEquals(originalMessage.content, convertedMessage.content)
        assertEquals(originalMessage.timestamp, convertedMessage.timestamp)
        assertEquals(originalMessage.type, convertedMessage.type)
        assertEquals(originalMessage.isRead, convertedMessage.isRead)
        assertEquals(originalMessage.threadId, convertedMessage.threadId)
        assertEquals(originalMessage.deviceId, convertedMessage.deviceId)
        assertEquals(originalMessage.syncStatus, convertedMessage.syncStatus)
    }
}
