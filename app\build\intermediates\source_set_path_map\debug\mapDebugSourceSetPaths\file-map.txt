com.example.smsbridge.app-work-runtime-2.9.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\res
com.example.smsbridge.app-material-icons-core-release-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\01073f4e3ce91005a588204341f2c60e\transformed\material-icons-core-release\res
com.example.smsbridge.app-ui-test-manifest-1.7.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\res
com.example.smsbridge.app-activity-compose-1.8.2-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\021c922a74586cc13a58371cbd9e950c\transformed\activity-compose-1.8.2\res
com.example.smsbridge.app-startup-runtime-1.1.1-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\041c913b04a56962d8a5d441a0eb19a0\transformed\startup-runtime-1.1.1\res
com.example.smsbridge.app-savedstate-ktx-1.2.1-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\0a6efcd6e944944f46cc97a4ab22ec90\transformed\savedstate-ktx-1.2.1\res
com.example.smsbridge.app-foundation-layout-release-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\12224fe584c6a32717904f03178b3116\transformed\foundation-layout-release\res
com.example.smsbridge.app-ui-tooling-release-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\res
com.example.smsbridge.app-lifecycle-runtime-release-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\1924d4f727be9683a508cf7378675631\transformed\lifecycle-runtime-release\res
com.example.smsbridge.app-profileinstaller-1.3.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\res
com.example.smsbridge.app-core-runtime-2.2.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\1c01120ed864c7e434cd04a70295ec04\transformed\core-runtime-2.2.0\res
com.example.smsbridge.app-graphics-path-1.0.1-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\23c2a5d8129e29e2bc7a7a1f34e6aa6a\transformed\graphics-path-1.0.1\res
com.example.smsbridge.app-lifecycle-livedata-core-ktx-2.8.4-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\23d96d8dc30edc1327a6b6700e70b904\transformed\lifecycle-livedata-core-ktx-2.8.4\res
com.example.smsbridge.app-emoji2-1.3.0-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\res
com.example.smsbridge.app-ui-geometry-release-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\42b4fc9cc354693548278aceb3117702\transformed\ui-geometry-release\res
com.example.smsbridge.app-ui-unit-release-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\45b99c9bf7aa7fb2d3bdef1a9444225d\transformed\ui-unit-release\res
com.example.smsbridge.app-lifecycle-viewmodel-ktx-2.8.4-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\4892019ade97738c2d0518bb54470363\transformed\lifecycle-viewmodel-ktx-2.8.4\res
com.example.smsbridge.app-lifecycle-livedata-core-2.8.4-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\4892c09ace4e456e4e2a065e96c679b3\transformed\lifecycle-livedata-core-2.8.4\res
com.example.smsbridge.app-room-ktx-2.5.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\4f3b9103ba5813c87c6d88d003a932a3\transformed\room-ktx-2.5.0\res
com.example.smsbridge.app-lifecycle-viewmodel-compose-release-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\51dfddca8ba638c094fe1182d81b2b0c\transformed\lifecycle-viewmodel-compose-release\res
com.example.smsbridge.app-lifecycle-viewmodel-release-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\541de9f7a890346346177580997c6295\transformed\lifecycle-viewmodel-release\res
com.example.smsbridge.app-ui-text-release-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\558cf4b1102d84e2513b5975a52852b5\transformed\ui-text-release\res
com.example.smsbridge.app-material3-release-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\569a4848e6cd56af57d5651d94321d08\transformed\material3-release\res
com.example.smsbridge.app-ui-release-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\633a39a9b9a3ac22f07ac3578025771c\transformed\ui-release\res
com.example.smsbridge.app-customview-poolingcontainer-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\67e86e852064bb3ad15badc6d1beac16\transformed\customview-poolingcontainer-1.0.0\res
com.example.smsbridge.app-work-runtime-ktx-2.9.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\6a1462cc8605c2609c0bf4ab08c6deb1\transformed\work-runtime-ktx-2.9.0\res
com.example.smsbridge.app-sqlite-framework-2.3.0-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\6c44c0b713c87c60a00e87a06f2ffcd2\transformed\sqlite-framework-2.3.0\res
com.example.smsbridge.app-lifecycle-process-2.8.4-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\res
com.example.smsbridge.app-lifecycle-service-2.8.4-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef72e12342337972c57f6822ebd04b8\transformed\lifecycle-service-2.8.4\res
com.example.smsbridge.app-activity-ktx-1.8.2-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\711b4bb3e01ef9da68fe9bd783692769\transformed\activity-ktx-1.8.2\res
com.example.smsbridge.app-runtime-release-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\742db9bf6e4cc2a3647c38d1cffdd118\transformed\runtime-release\res
com.example.smsbridge.app-foundation-release-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\79a66074d77b36bc85e223e1c4ed5168\transformed\foundation-release\res
com.example.smsbridge.app-navigation-runtime-ktx-2.7.6-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\7b8cf9d816b4bd08abf78ab1bf1742cd\transformed\navigation-runtime-ktx-2.7.6\res
com.example.smsbridge.app-navigation-runtime-2.7.6-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\7f3b40ac08aca7ec7ef460df10d7dbca\transformed\navigation-runtime-2.7.6\res
com.example.smsbridge.app-animation-core-release-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\85b69d03cdbe9b2b25d49d09e8a5debc\transformed\animation-core-release\res
com.example.smsbridge.app-ui-tooling-data-release-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\8889440980a17a5e88a63467039bea7e\transformed\ui-tooling-data-release\res
com.example.smsbridge.app-datastore-1.0.0-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\8e35bd8de798a3fec06a2816005c8114\transformed\datastore-1.0.0\res
com.example.smsbridge.app-savedstate-1.2.1-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\9077a541dbc7af6444802377ae9e2c64\transformed\savedstate-1.2.1\res
com.example.smsbridge.app-navigation-compose-2.7.6-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\95be2e78697e7bcd488a9e6a0d025be4\transformed\navigation-compose-2.7.6\res
com.example.smsbridge.app-lifecycle-runtime-ktx-release-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\999def391aa6484bb48a56dfd56f9ef8\transformed\lifecycle-runtime-ktx-release\res
com.example.smsbridge.app-hilt-navigation-compose-1.1.0-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\9f65c66558cc6d096526f6faff00dc9e\transformed\hilt-navigation-compose-1.1.0\res
com.example.smsbridge.app-animation-release-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\aa4bea1c9cac9beb0a1e8fddb70b6247\transformed\animation-release\res
com.example.smsbridge.app-navigation-common-2.7.6-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\ac618beab86753b6fb93ecfbfd6949f1\transformed\navigation-common-2.7.6\res
com.example.smsbridge.app-annotation-experimental-1.4.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\ad925207df37b84adb52f282303a2d0b\transformed\annotation-experimental-1.4.0\res
com.example.smsbridge.app-navigation-common-ktx-2.7.6-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\b4eaf21a5f7540a07df1b3967c1f3d09\transformed\navigation-common-ktx-2.7.6\res
com.example.smsbridge.app-ui-tooling-preview-release-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\bbbb6c859f6cbc42810a25628a5ef6b9\transformed\ui-tooling-preview-release\res
com.example.smsbridge.app-lifecycle-viewmodel-savedstate-2.8.4-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\be43d6172353f7513543206d2cbe432a\transformed\lifecycle-viewmodel-savedstate-2.8.4\res
com.example.smsbridge.app-datastore-preferences-1.0.0-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f537cd72a11debb129543fb4d2b44b\transformed\datastore-preferences-1.0.0\res
com.example.smsbridge.app-core-ktx-1.13.1-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\c241fe8bc39be0739d96323ae1d70a51\transformed\core-ktx-1.13.1\res
com.example.smsbridge.app-fragment-1.5.1-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\c4b01e9cd3fa56adc9989cf64ce3346f\transformed\fragment-1.5.1\res
com.example.smsbridge.app-ui-graphics-release-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\c9881bd69f50d320d31806a354955f54\transformed\ui-graphics-release\res
com.example.smsbridge.app-hilt-navigation-1.1.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\cd773884fc6158e38de3b1f8b8e10483\transformed\hilt-navigation-1.1.0\res
com.example.smsbridge.app-activity-1.8.2-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\d0eb922676c99d77089f9e796b49236d\transformed\activity-1.8.2\res
com.example.smsbridge.app-material-release-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\d35018ac0925c576d257d08d0f22c558\transformed\material-release\res
com.example.smsbridge.app-lifecycle-runtime-compose-release-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ac125ed70ce4b086ace91f075b3157\transformed\lifecycle-runtime-compose-release\res
com.example.smsbridge.app-lifecycle-livedata-2.8.4-55 C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c632b2f8f0f70a31b9cb1e7c34c8f8\transformed\lifecycle-livedata-2.8.4\res
com.example.smsbridge.app-runtime-saveable-release-56 C:\Users\<USER>\.gradle\caches\8.13\transforms\da488a8ffec6746e9fc247368233399a\transformed\runtime-saveable-release\res
com.example.smsbridge.app-ui-util-release-57 C:\Users\<USER>\.gradle\caches\8.13\transforms\de5f04198540afe3e4dc1313e3bcfb32\transformed\ui-util-release\res
com.example.smsbridge.app-material-ripple-release-58 C:\Users\<USER>\.gradle\caches\8.13\transforms\deb758cdd33538441266bf499f5dc3f0\transformed\material-ripple-release\res
com.example.smsbridge.app-sqlite-2.3.0-59 C:\Users\<USER>\.gradle\caches\8.13\transforms\e38699a377b648ce8cbdbc16589f68f5\transformed\sqlite-2.3.0\res
com.example.smsbridge.app-room-runtime-2.5.0-60 C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\res
com.example.smsbridge.app-core-1.13.1-61 C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\res
com.example.smsbridge.app-browser-1.8.0-62 C:\Users\<USER>\.gradle\caches\8.13\transforms\fd357763fc5d12aea42dedd83b4a8eb5\transformed\browser-1.8.0\res
com.example.smsbridge.app-pngs-63 C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\build\generated\res\pngs\debug
com.example.smsbridge.app-resValues-64 C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\build\generated\res\resValues\debug
com.example.smsbridge.app-packageDebugResources-65 C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.smsbridge.app-packageDebugResources-66 C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.smsbridge.app-debug-67 C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.smsbridge.app-debug-68 C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\debug\res
com.example.smsbridge.app-main-69 C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\res
