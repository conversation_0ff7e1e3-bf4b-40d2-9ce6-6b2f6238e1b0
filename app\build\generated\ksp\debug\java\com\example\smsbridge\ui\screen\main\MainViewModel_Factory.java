package com.example.smsbridge.ui.screen.main;

import androidx.work.WorkManager;
import com.example.smsbridge.data.local.SmsContentProvider;
import com.example.smsbridge.data.preferences.PreferencesManager;
import com.example.smsbridge.data.remote.SupabaseConfig;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<SmsContentProvider> smsContentProvider;

  private final Provider<PreferencesManager> preferencesManagerProvider;

  private final Provider<SupabaseConfig> supabaseConfigProvider;

  private final Provider<WorkManager> workManagerProvider;

  public MainViewModel_Factory(Provider<SmsContentProvider> smsContentProvider,
      Provider<PreferencesManager> preferencesManagerProvider,
      Provider<SupabaseConfig> supabaseConfigProvider, Provider<WorkManager> workManagerProvider) {
    this.smsContentProvider = smsContentProvider;
    this.preferencesManagerProvider = preferencesManagerProvider;
    this.supabaseConfigProvider = supabaseConfigProvider;
    this.workManagerProvider = workManagerProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(smsContentProvider.get(), preferencesManagerProvider.get(), supabaseConfigProvider.get(), workManagerProvider.get());
  }

  public static MainViewModel_Factory create(Provider<SmsContentProvider> smsContentProvider,
      Provider<PreferencesManager> preferencesManagerProvider,
      Provider<SupabaseConfig> supabaseConfigProvider, Provider<WorkManager> workManagerProvider) {
    return new MainViewModel_Factory(smsContentProvider, preferencesManagerProvider, supabaseConfigProvider, workManagerProvider);
  }

  public static MainViewModel newInstance(SmsContentProvider smsContentProvider,
      PreferencesManager preferencesManager, SupabaseConfig supabaseConfig,
      WorkManager workManager) {
    return new MainViewModel(smsContentProvider, preferencesManager, supabaseConfig, workManager);
  }
}
