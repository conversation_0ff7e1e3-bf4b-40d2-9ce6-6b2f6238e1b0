{"logs": [{"outputFile": "com.example.smsbridge.app-mergeDebugResources-65:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f116294e462fcef23dad22a63f303a87\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "2,3,4,5,6,7,8,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,400,499,607,712,8776", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "195,297,395,494,602,707,828,8872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd357763fc5d12aea42dedd83b4a8eb5\\transformed\\browser-1.8.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1014,1397,1497,1610", "endColumns": "99,99,112,97", "endOffsets": "1109,1492,1605,1703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79a66074d77b36bc85e223e1c4ed5168\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "89,90", "startColumns": "4,4", "startOffsets": "9142,9232", "endColumns": "89,88", "endOffsets": "9227,9316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\633a39a9b9a3ac22f07ac3578025771c\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,989,1079,1148,1222,1293,1363,1441,1508", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,984,1074,1143,1217,1288,1358,1436,1503,1623"}, "to": {"startLines": "9,10,12,13,14,18,19,77,78,79,80,81,82,83,84,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "833,926,1114,1210,1309,1708,1784,8146,8235,8316,8402,8492,8561,8635,8706,8877,8955,9022", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "921,1009,1205,1304,1392,1779,1867,8230,8311,8397,8487,8556,8630,8701,8771,8950,9017,9137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\569a4848e6cd56af57d5651d94321d08\\transformed\\material3-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4699,4783,4868,4969,5049,5134,5233,5333,5428,5528,5615,5719,5820,5924,6046,6126,6230", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4694,4778,4863,4964,5044,5129,5228,5328,5423,5523,5610,5714,5815,5919,6041,6121,6225,6324"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1872,2011,2144,2251,2383,2499,2595,2708,2852,2976,3131,3216,3315,3405,3499,3613,3735,3839,3972,4099,4234,4406,4534,4652,4778,4898,4989,5087,5205,5344,5440,5548,5651,5784,5927,6033,6130,6210,6308,6400,6516,6600,6685,6786,6866,6951,7050,7150,7245,7345,7432,7536,7637,7741,7863,7943,8047", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "2006,2139,2246,2378,2494,2590,2703,2847,2971,3126,3211,3310,3400,3494,3608,3730,3834,3967,4094,4229,4401,4529,4647,4773,4893,4984,5082,5200,5339,5435,5543,5646,5779,5922,6028,6125,6205,6303,6395,6511,6595,6680,6781,6861,6946,7045,7145,7240,7340,7427,7531,7632,7736,7858,7938,8042,8141"}}]}]}