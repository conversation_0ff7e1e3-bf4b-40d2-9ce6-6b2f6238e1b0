# Supabase Setup Guide for SMS Bridge

This guide will walk you through setting up Supabase for the SMS Bridge Android app.

## Step 1: Create a Supabase Account and Project

1. Go to [https://supabase.com](https://supabase.com)
2. Click "Start your project" or "Sign Up"
3. Sign up with GitHub, Google, or email
4. Once logged in, click "New Project"
5. Choose your organization (or create one)
6. Fill in project details:
   - **Name**: SMS Bridge (or any name you prefer)
   - **Database Password**: Choose a strong password (save this!)
   - **Region**: Choose the region closest to you
7. Click "Create new project"
8. Wait for the project to be set up (this takes a few minutes)

## Step 2: Set Up Database Tables

1. In your Supabase dashboard, navigate to the **SQL Editor** (in the left sidebar)
2. Click "New Query"
3. Copy and paste the following SQL code:

```sql
-- Create SMS messages table
CREATE TABLE IF NOT EXISTS sms_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    android_id BIGINT,
    phone_number TEXT NOT NULL,
    content TEXT NOT NULL,
    timestamp BIGINT NOT NULL,
    type TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    thread_id BIGINT,
    device_id TEXT,
    sync_status TEXT DEFAULT 'PENDING',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sms_messages_phone_number ON sms_messages(phone_number);
CREATE INDEX IF NOT EXISTS idx_sms_messages_timestamp ON sms_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_sms_messages_device_id ON sms_messages(device_id);
CREATE INDEX IF NOT EXISTS idx_sms_messages_sync_status ON sms_messages(sync_status);

-- Create sync logs table for tracking sync operations
CREATE TABLE IF NOT EXISTS sync_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    device_id TEXT NOT NULL,
    sync_type TEXT NOT NULL, -- 'FULL', 'INCREMENTAL', 'REAL_TIME'
    messages_processed INTEGER DEFAULT 0,
    messages_successful INTEGER DEFAULT 0,
    messages_failed INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'RUNNING', -- 'RUNNING', 'COMPLETED', 'FAILED'
    error_message TEXT
);

-- Create index for sync logs
CREATE INDEX IF NOT EXISTS idx_sync_logs_device_id ON sync_logs(device_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_started_at ON sync_logs(started_at);

-- Enable Row Level Security (RLS) for better security
ALTER TABLE sms_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users
CREATE POLICY "Allow all operations for authenticated users" ON sms_messages
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON sync_logs
    FOR ALL USING (auth.role() = 'authenticated');
```

4. Click "Run" to execute the SQL
5. You should see "Success. No rows returned" message

## Step 3: Configure Row Level Security (Optional but Recommended)

If you want to allow anonymous access (less secure but simpler), you can create policies that allow all operations:

```sql
-- Alternative: Allow anonymous access (less secure)
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON sms_messages;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON sync_logs;

CREATE POLICY "Allow all operations" ON sms_messages FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON sync_logs FOR ALL USING (true);
```

## Step 4: Get Your API Credentials

1. In your Supabase dashboard, go to **Settings** → **API** (in the left sidebar)
2. You'll see your project credentials:
   - **Project URL**: Something like `https://abcdefghijklmnop.supabase.co`
   - **API Keys**: 
     - `anon` `public` key (this is what you need for the app)
     - `service_role` `secret` key (don't use this in the app)

3. Copy both the **Project URL** and the **anon public key**

## Step 5: Test Your Setup (Optional)

You can test your setup using the Supabase dashboard:

1. Go to **Table Editor** in the left sidebar
2. You should see your `sms_messages` and `sync_logs` tables
3. Try inserting a test record into `sms_messages`:
   - Click on `sms_messages` table
   - Click "Insert" → "Insert row"
   - Fill in some test data:
     - `phone_number`: "+1234567890"
     - `content`: "Test message"
     - `timestamp`: 1640995200000 (or any timestamp)
     - `type`: "INBOX"
   - Click "Save"
4. If successful, you'll see the test record in the table

## Step 6: Configure the Android App

Now you can configure the Android app with your Supabase credentials:

### Method 1: Through the App Settings (Recommended)
1. Install and launch the SMS Bridge app
2. Complete the permission setup
3. Navigate to Settings
4. Enter your **Project URL** and **anon public key**
5. Click "Save Configuration"

### Method 2: Hardcode in Source Code (Development Only)
1. Open `app/src/main/java/com/example/smsbridge/data/remote/SupabaseConfig.kt`
2. Replace the placeholder values:
```kotlin
private const val SUPABASE_URL = "https://your-project-id.supabase.co"
private const val SUPABASE_ANON_KEY = "your-anon-key-here"
```

## Security Best Practices

1. **Never commit API keys to version control**
2. **Use Row Level Security (RLS)** to protect your data
3. **Regularly rotate your API keys** if needed
4. **Monitor your Supabase usage** in the dashboard
5. **Set up database backups** in Supabase settings

## Troubleshooting

### Common Issues

**"relation 'sms_messages' does not exist"**
- Make sure you ran the SQL setup script correctly
- Check the SQL Editor for any error messages

**"JWT expired" or authentication errors**
- Your API key might be incorrect
- Make sure you're using the `anon` `public` key, not the `service_role` key

**"Row Level Security policy violation"**
- If using RLS, make sure your policies are set up correctly
- For testing, you can temporarily use the anonymous access policies

**Connection timeout**
- Check your internet connection
- Verify the Project URL is correct
- Make sure your Supabase project is active

### Getting Help

1. Check the [Supabase Documentation](https://supabase.com/docs)
2. Visit the [Supabase Community](https://github.com/supabase/supabase/discussions)
3. Check the SMS Bridge app logs for specific error messages

## Next Steps

Once your Supabase setup is complete:

1. Install the SMS Bridge Android app
2. Grant the required permissions
3. Configure the app with your Supabase credentials
4. Perform an initial sync to test the connection
5. Enable automatic sync for ongoing message backup

Your SMS messages will now be securely backed up to your personal Supabase database!
