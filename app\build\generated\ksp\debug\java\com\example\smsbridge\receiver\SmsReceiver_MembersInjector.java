package com.example.smsbridge.receiver;

import androidx.work.WorkManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SmsReceiver_MembersInjector implements MembersInjector<SmsReceiver> {
  private final Provider<WorkManager> workManagerProvider;

  public SmsReceiver_MembersInjector(Provider<WorkManager> workManagerProvider) {
    this.workManagerProvider = workManagerProvider;
  }

  public static MembersInjector<SmsReceiver> create(Provider<WorkManager> workManagerProvider) {
    return new SmsReceiver_MembersInjector(workManagerProvider);
  }

  @Override
  public void injectMembers(SmsReceiver instance) {
    injectWorkManager(instance, workManagerProvider.get());
  }

  @InjectedFieldSignature("com.example.smsbridge.receiver.SmsReceiver.workManager")
  public static void injectWorkManager(SmsReceiver instance, WorkManager workManager) {
    instance.workManager = workManager;
  }
}
