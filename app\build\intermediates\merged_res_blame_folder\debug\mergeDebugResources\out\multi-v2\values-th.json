{"logs": [{"outputFile": "com.example.smsbridge.app-mergeDebugResources-65:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\633a39a9b9a3ac22f07ac3578025771c\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,979,1068,1141,1216,1292,1368,1446,1513", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,974,1063,1136,1211,1287,1363,1441,1508,1631"}, "to": {"startLines": "9,10,12,13,14,18,19,77,78,79,80,81,82,83,84,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,906,1086,1183,1284,1680,1765,7970,8056,8139,8225,8314,8387,8462,8538,8715,8793,8860", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "901,978,1178,1279,1367,1760,1845,8051,8134,8220,8309,8382,8457,8533,8609,8788,8855,8978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f116294e462fcef23dad22a63f303a87\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "2,3,4,5,6,7,8,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,402,500,603,708,8614", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "196,299,397,495,598,703,815,8710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd357763fc5d12aea42dedd83b4a8eb5\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "983,1372,1471,1582", "endColumns": "102,98,110,97", "endOffsets": "1081,1466,1577,1675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79a66074d77b36bc85e223e1c4ed5168\\transformed\\foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "89,90", "startColumns": "4,4", "startOffsets": "8983,9080", "endColumns": "96,94", "endOffsets": "9075,9170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\569a4848e6cd56af57d5651d94321d08\\transformed\\material3-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,393,505,604,697,807,937,1061,1202,1288,1388,1479,1577,1695,1811,1916,2043,2167,2295,2447,2570,2688,2812,2933,3025,3124,3236,3369,3465,3583,3690,3816,3950,4060,4158,4239,4333,4427,4534,4620,4703,4808,4888,4975,5074,5176,5270,5374,5460,5561,5659,5762,5879,5959,6069", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "163,275,388,500,599,692,802,932,1056,1197,1283,1383,1474,1572,1690,1806,1911,2038,2162,2290,2442,2565,2683,2807,2928,3020,3119,3231,3364,3460,3578,3685,3811,3945,4055,4153,4234,4328,4422,4529,4615,4698,4803,4883,4970,5069,5171,5265,5369,5455,5556,5654,5757,5874,5954,6064,6170"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1850,1963,2075,2188,2300,2399,2492,2602,2732,2856,2997,3083,3183,3274,3372,3490,3606,3711,3838,3962,4090,4242,4365,4483,4607,4728,4820,4919,5031,5164,5260,5378,5485,5611,5745,5855,5953,6034,6128,6222,6329,6415,6498,6603,6683,6770,6869,6971,7065,7169,7255,7356,7454,7557,7674,7754,7864", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "1958,2070,2183,2295,2394,2487,2597,2727,2851,2992,3078,3178,3269,3367,3485,3601,3706,3833,3957,4085,4237,4360,4478,4602,4723,4815,4914,5026,5159,5255,5373,5480,5606,5740,5850,5948,6029,6123,6217,6324,6410,6493,6598,6678,6765,6864,6966,7060,7164,7250,7351,7449,7552,7669,7749,7859,7965"}}]}]}