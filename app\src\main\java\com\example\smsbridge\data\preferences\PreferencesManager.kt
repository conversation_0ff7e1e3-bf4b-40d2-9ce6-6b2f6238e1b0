package com.example.smsbridge.data.preferences

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for app preferences using DataStore
 */
@Singleton
class PreferencesManager @Inject constructor(
    private val dataStore: DataStore<Preferences>
) {
    
    companion object {
        private val KEY_AUTO_SYNC_ENABLED = booleanPreferencesKey("auto_sync_enabled")
        private val KEY_REAL_TIME_SYNC_ENABLED = booleanPreferencesKey("real_time_sync_enabled")
        private val KEY_SYNC_ON_WIFI_ONLY = booleanPreferencesKey("sync_on_wifi_only")
        private val KEY_LAST_SYNC_TIMESTAMP = longPreferencesKey("last_sync_timestamp")
        private val KEY_DEVICE_ID = stringPreferencesKey("device_id")
        private val KEY_SUPABASE_URL = stringPreferencesKey("supabase_url")
        private val KEY_SUPABASE_ANON_KEY = stringPreferencesKey("supabase_anon_key")
        private val KEY_SYNC_FREQUENCY_HOURS = longPreferencesKey("sync_frequency_hours")
        private val KEY_FIRST_LAUNCH = booleanPreferencesKey("first_launch")
        private val KEY_PERMISSIONS_EXPLAINED = booleanPreferencesKey("permissions_explained")
    }
    
    // Auto sync settings
    val isAutoSyncEnabled: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[KEY_AUTO_SYNC_ENABLED] ?: true
    }
    
    val isRealTimeSyncEnabled: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[KEY_REAL_TIME_SYNC_ENABLED] ?: true
    }
    
    val isSyncOnWifiOnly: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[KEY_SYNC_ON_WIFI_ONLY] ?: false
    }
    
    // Sync tracking
    val lastSyncTimestamp: Flow<Long> = dataStore.data.map { preferences ->
        preferences[KEY_LAST_SYNC_TIMESTAMP] ?: 0L
    }
    
    val syncFrequencyHours: Flow<Long> = dataStore.data.map { preferences ->
        preferences[KEY_SYNC_FREQUENCY_HOURS] ?: 24L // Default: sync every 24 hours
    }
    
    // Device and configuration
    val deviceId: Flow<String?> = dataStore.data.map { preferences ->
        preferences[KEY_DEVICE_ID]
    }
    
    val supabaseUrl: Flow<String?> = dataStore.data.map { preferences ->
        preferences[KEY_SUPABASE_URL]
    }
    
    val supabaseAnonKey: Flow<String?> = dataStore.data.map { preferences ->
        preferences[KEY_SUPABASE_ANON_KEY]
    }
    
    // App state
    val isFirstLaunch: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[KEY_FIRST_LAUNCH] ?: true
    }
    
    val arePermissionsExplained: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[KEY_PERMISSIONS_EXPLAINED] ?: false
    }
    
    // Setters
    suspend fun setAutoSyncEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[KEY_AUTO_SYNC_ENABLED] = enabled
        }
    }
    
    suspend fun setRealTimeSyncEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[KEY_REAL_TIME_SYNC_ENABLED] = enabled
        }
    }
    
    suspend fun setSyncOnWifiOnly(wifiOnly: Boolean) {
        dataStore.edit { preferences ->
            preferences[KEY_SYNC_ON_WIFI_ONLY] = wifiOnly
        }
    }
    
    suspend fun setLastSyncTimestamp(timestamp: Long) {
        dataStore.edit { preferences ->
            preferences[KEY_LAST_SYNC_TIMESTAMP] = timestamp
        }
    }
    
    suspend fun setSyncFrequencyHours(hours: Long) {
        dataStore.edit { preferences ->
            preferences[KEY_SYNC_FREQUENCY_HOURS] = hours
        }
    }
    
    suspend fun setDeviceId(deviceId: String) {
        dataStore.edit { preferences ->
            preferences[KEY_DEVICE_ID] = deviceId
        }
    }
    
    suspend fun setSupabaseConfig(url: String, anonKey: String) {
        dataStore.edit { preferences ->
            preferences[KEY_SUPABASE_URL] = url
            preferences[KEY_SUPABASE_ANON_KEY] = anonKey
        }
    }
    
    suspend fun setFirstLaunch(isFirstLaunch: Boolean) {
        dataStore.edit { preferences ->
            preferences[KEY_FIRST_LAUNCH] = isFirstLaunch
        }
    }
    
    suspend fun setPermissionsExplained(explained: Boolean) {
        dataStore.edit { preferences ->
            preferences[KEY_PERMISSIONS_EXPLAINED] = explained
        }
    }
    
    suspend fun clearAllPreferences() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }
}
