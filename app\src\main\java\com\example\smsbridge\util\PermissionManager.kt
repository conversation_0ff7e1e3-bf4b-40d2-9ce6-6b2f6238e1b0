package com.example.smsbridge.util

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for handling SMS-related permissions
 */
@Singleton
class PermissionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.READ_SMS,
            Manifest.permission.RECEIVE_SMS,
            Manifest.permission.READ_PHONE_STATE
        )
        
        val PERMISSION_EXPLANATIONS = mapOf(
            Manifest.permission.READ_SMS to "This permission allows the app to read your existing SMS messages to sync them to the cloud.",
            Manifest.permission.RECEIVE_SMS to "This permission allows the app to receive new SMS messages in real-time for immediate syncing.",
            Manifest.permission.READ_PHONE_STATE to "This permission helps identify your device for proper message organization."
        )
    }
    
    /**
     * Check if all required permissions are granted
     */
    fun areAllPermissionsGranted(): Boolean {
        return REQUIRED_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * Check if a specific permission is granted
     */
    fun isPermissionGranted(permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * Get list of permissions that are not granted
     */
    fun getMissingPermissions(): List<String> {
        return REQUIRED_PERMISSIONS.filter { permission ->
            !isPermissionGranted(permission)
        }
    }
    
    /**
     * Get explanation for a specific permission
     */
    fun getPermissionExplanation(permission: String): String {
        return PERMISSION_EXPLANATIONS[permission] ?: "This permission is required for the app to function properly."
    }
    
    /**
     * Get all permission explanations
     */
    fun getAllPermissionExplanations(): Map<String, String> {
        return PERMISSION_EXPLANATIONS
    }
    
    /**
     * Check if SMS reading permission is granted
     */
    fun canReadSms(): Boolean {
        return isPermissionGranted(Manifest.permission.READ_SMS)
    }
    
    /**
     * Check if SMS receiving permission is granted
     */
    fun canReceiveSms(): Boolean {
        return isPermissionGranted(Manifest.permission.RECEIVE_SMS)
    }
    
    /**
     * Check if phone state permission is granted
     */
    fun canReadPhoneState(): Boolean {
        return isPermissionGranted(Manifest.permission.READ_PHONE_STATE)
    }
    
    /**
     * Get user-friendly permission names
     */
    fun getPermissionDisplayName(permission: String): String {
        return when (permission) {
            Manifest.permission.READ_SMS -> "Read SMS Messages"
            Manifest.permission.RECEIVE_SMS -> "Receive SMS Messages"
            Manifest.permission.READ_PHONE_STATE -> "Read Phone State"
            else -> permission
        }
    }
}
