{"logs": [{"outputFile": "com.example.smsbridge.app-mergeDebugResources-65:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1924d4f727be9683a508cf7378675631\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "6815", "endColumns": "42", "endOffsets": "6853"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SMSBridge\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "47", "endOffsets": "59"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "7365", "endColumns": "47", "endOffsets": "7408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd357763fc5d12aea42dedd83b4a8eb5\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "10,11,12,13,24,25,132,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "672,730,796,859,1457,1528,8043,8336,8403,8482", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "725,791,854,916,1523,1595,8106,8398,8477,8546"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SMSBridge\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "9,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "630,1184,1231,1278,1325,1370,1415", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "667,1226,1273,1320,1365,1410,1452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9077a541dbc7af6444802377ae9e2c64\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "6918", "endColumns": "53", "endOffsets": "6967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\00285e1df03d11f6a2bc73bd2b29fc52\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "210,275,345,409", "endColumns": "64,69,63,60", "endOffsets": "270,340,404,465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79a66074d77b36bc85e223e1c4ed5168\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "214,215", "startColumns": "4,4", "startOffsets": "13742,13798", "endColumns": "55,54", "endOffsets": "13793,13848"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\SMSBridge\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "86", "endOffsets": "138"}, "to": {"startLines": "250", "startColumns": "4", "startOffsets": "15897", "endColumns": "85", "endOffsets": "15978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\541de9f7a890346346177580997c6295\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "6972", "endColumns": "49", "endOffsets": "7017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7f3b40ac08aca7ec7ef460df10d7dbca\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "93,253,406,409", "startColumns": "4,4,4,4", "startOffsets": "5729,16122,21773,21888", "endLines": "93,259,408,411", "endColumns": "52,24,24,24", "endOffsets": "5777,16421,21883,21998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\633a39a9b9a3ac22f07ac3578025771c\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "83,84,85,88,89,118,130,131,133,134,135,139,140,202,203,204,205,206,207,208,209,211,212,213,216,232,235", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5189,5263,5321,5499,5550,7086,7924,7989,8111,8177,8278,8551,8603,13104,13166,13220,13270,13324,13370,13416,13458,13569,13616,13652,13853,14833,14944", "endLines": "83,84,85,88,89,118,130,131,133,134,135,139,140,202,203,204,205,206,207,208,209,211,212,213,218,234,238", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "5258,5316,5371,5545,5600,7134,7984,8038,8172,8273,8331,8598,8658,13161,13215,13265,13319,13365,13411,13453,13493,13611,13647,13737,13960,14939,15134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\67e86e852064bb3ad15badc6d1beac16\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "90,94", "startColumns": "4,4", "startOffsets": "5605,5782", "endColumns": "53,66", "endOffsets": "5654,5844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\041c913b04a56962d8a5d441a0eb19a0\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "121", "startColumns": "4", "startOffsets": "7282", "endColumns": "82", "endOffsets": "7360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0eb922676c99d77089f9e796b49236d\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "95,114", "startColumns": "4,4", "startOffsets": "5849,6858", "endColumns": "41,59", "endOffsets": "5886,6913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\569a4848e6cd56af57d5651d94321d08\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "119,141,142,143,144,145,146,147,148,149,150,153,154,155,156,157,158,159,160,161,162,163,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,219,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7139,8663,8751,8837,8918,9002,9071,9136,9219,9325,9411,9531,9585,9654,9715,9784,9873,9968,10042,10139,10232,10330,10479,10570,10658,10754,10852,10916,10984,11071,11165,11232,11304,11376,11477,11586,11662,11731,11779,11845,11909,11983,12040,12097,12169,12219,12273,12344,12415,12485,12554,12612,12688,12759,12833,12919,12969,13039,13965,14680", "endLines": "119,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157,158,159,160,161,162,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,228,231", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7207,8746,8832,8913,8997,9066,9131,9214,9320,9406,9526,9580,9649,9710,9779,9868,9963,10037,10134,10227,10325,10474,10565,10653,10749,10847,10911,10979,11066,11160,11227,11299,11371,11472,11581,11657,11726,11774,11840,11904,11978,12035,12092,12164,12214,12268,12339,12410,12480,12549,12607,12683,12754,12828,12914,12964,13034,13099,14675,14828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac618beab86753b6fb93ecfbfd6949f1\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "378,391,397,403,412", "startColumns": "4,4,4,4,4", "startOffsets": "20510,21149,21393,21640,22003", "endLines": "390,396,402,405,416", "endColumns": "24,24,24,24,24", "endOffsets": "21144,21388,21635,21768,22180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f116294e462fcef23dad22a63f303a87\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,7,8,14,15,16,17,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,91,92,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,120,123,124,125,126,127,128,129,210,239,240,244,245,249,251,252,260,266,276,309,339,372", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,470,542,921,986,1052,1121,1600,1670,1738,1810,1880,1941,2015,2088,2149,2210,2272,2336,2398,2459,2527,2627,2687,2753,2826,2895,2952,3004,3066,3138,3214,3279,3338,3397,3457,3517,3577,3637,3697,3757,3817,3877,3937,3997,4056,4116,4176,4236,4296,4356,4416,4476,4536,4596,4656,4715,4775,4835,4894,4953,5012,5071,5130,5659,5694,5956,6011,6074,6129,6187,6245,6306,6369,6426,6477,6527,6588,6645,6711,6745,6780,7212,7413,7480,7552,7621,7690,7764,7836,13498,15139,15256,15457,15567,15768,15983,16055,16426,16629,16930,18661,19661,20343", "endLines": "2,7,8,14,15,16,17,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,91,92,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,120,123,124,125,126,127,128,129,210,239,243,244,248,249,251,252,265,275,308,329,371,377", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,537,625,981,1047,1116,1179,1665,1733,1805,1875,1936,2010,2083,2144,2205,2267,2331,2393,2454,2522,2622,2682,2748,2821,2890,2947,2999,3061,3133,3209,3274,3333,3392,3452,3512,3572,3632,3692,3752,3812,3872,3932,3992,4051,4111,4171,4231,4291,4351,4411,4471,4531,4591,4651,4710,4770,4830,4889,4948,5007,5066,5125,5184,5689,5724,6006,6069,6124,6182,6240,6301,6364,6421,6472,6522,6583,6640,6706,6740,6775,6810,7277,7475,7547,7616,7685,7759,7831,7919,13564,15251,15452,15562,15763,15892,16050,16117,16624,16925,18656,19337,20338,20505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c4b01e9cd3fa56adc9989cf64ce3346f\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "86,96,117,330,335", "startColumns": "4,4,4,4,4", "startOffsets": "5376,5891,7022,19342,19512", "endLines": "86,96,117,334,338", "endColumns": "56,64,63,24,24", "endOffsets": "5428,5951,7081,19507,19656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9881bd69f50d320d31806a354955f54\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "87", "startColumns": "4", "startOffsets": "5433", "endColumns": "65", "endOffsets": "5494"}}]}]}