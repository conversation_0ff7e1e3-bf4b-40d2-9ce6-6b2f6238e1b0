package com.example.smsbridge.data.remote

import com.example.smsbridge.BuildConfig
import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.GoTrue
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.realtime.Realtime
import io.ktor.client.engine.android.Android
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Configuration class for Supabase client
 */
@Singleton
class SupabaseConfig @Inject constructor() {
    
    companion object {
        // Supabase configuration from BuildConfig
        private val SUPABASE_URL = BuildConfig.SUPABASE_URL
        private val SUPABASE_ANON_KEY = BuildConfig.SUPABASE_ANON_KEY

        // Table names
        const val SMS_MESSAGES_TABLE = "sms_messages"
        const val SYNC_LOGS_TABLE = "sync_logs"
    }
    
    val client: SupabaseClient by lazy {
        createSupabaseClient(
            supabaseUrl = SUPABASE_URL,
            supabaseKey = SUPABASE_ANON_KEY
        ) {
            // Configure HTTP client
            httpEngine = Android.create()
            
            // Install Postgrest for database operations
            install(Postgrest)
            
            // Install GoTrue for authentication (optional)
            install(GoTrue)
            
            // Install Realtime for real-time updates (optional)
            install(Realtime)
        }
    }
    
    /**
     * Check if Supabase is properly configured
     */
    fun isConfigured(): Boolean {
        return SUPABASE_URL != "YOUR_SUPABASE_URL" &&
               SUPABASE_ANON_KEY != "YOUR_SUPABASE_ANON_KEY" &&
               SUPABASE_URL.isNotBlank() &&
               SUPABASE_ANON_KEY.isNotBlank() &&
               SUPABASE_URL.startsWith("https://")
    }
}

/**
 * SQL schema for creating the SMS messages table in Supabase
 * 
 * Run this SQL in your Supabase SQL editor:
 * 
 * -- Create SMS messages table
 * CREATE TABLE IF NOT EXISTS sms_messages (
 *     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
 *     android_id BIGINT,
 *     phone_number TEXT NOT NULL,
 *     content TEXT NOT NULL,
 *     timestamp BIGINT NOT NULL,
 *     type TEXT NOT NULL,
 *     is_read BOOLEAN DEFAULT FALSE,
 *     thread_id BIGINT,
 *     device_id TEXT,
 *     sync_status TEXT DEFAULT 'PENDING',
 *     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
 *     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
 * );
 * 
 * -- Create index for faster queries
 * CREATE INDEX IF NOT EXISTS idx_sms_messages_phone_number ON sms_messages(phone_number);
 * CREATE INDEX IF NOT EXISTS idx_sms_messages_timestamp ON sms_messages(timestamp);
 * CREATE INDEX IF NOT EXISTS idx_sms_messages_device_id ON sms_messages(device_id);
 * CREATE INDEX IF NOT EXISTS idx_sms_messages_sync_status ON sms_messages(sync_status);
 * 
 * -- Create sync logs table for tracking sync operations
 * CREATE TABLE IF NOT EXISTS sync_logs (
 *     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
 *     device_id TEXT NOT NULL,
 *     sync_type TEXT NOT NULL, -- 'FULL', 'INCREMENTAL', 'REAL_TIME'
 *     messages_processed INTEGER DEFAULT 0,
 *     messages_successful INTEGER DEFAULT 0,
 *     messages_failed INTEGER DEFAULT 0,
 *     started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
 *     completed_at TIMESTAMP WITH TIME ZONE,
 *     status TEXT DEFAULT 'RUNNING', -- 'RUNNING', 'COMPLETED', 'FAILED'
 *     error_message TEXT
 * );
 * 
 * -- Create index for sync logs
 * CREATE INDEX IF NOT EXISTS idx_sync_logs_device_id ON sync_logs(device_id);
 * CREATE INDEX IF NOT EXISTS idx_sync_logs_started_at ON sync_logs(started_at);
 * 
 * -- Enable Row Level Security (RLS) for better security
 * ALTER TABLE sms_messages ENABLE ROW LEVEL SECURITY;
 * ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;
 * 
 * -- Create policies (adjust based on your authentication needs)
 * -- This example allows all operations for authenticated users
 * CREATE POLICY "Allow all operations for authenticated users" ON sms_messages
 *     FOR ALL USING (auth.role() = 'authenticated');
 * 
 * CREATE POLICY "Allow all operations for authenticated users" ON sync_logs
 *     FOR ALL USING (auth.role() = 'authenticated');
 * 
 * -- If you want to allow anonymous access (less secure), use:
 * -- CREATE POLICY "Allow all operations" ON sms_messages FOR ALL USING (true);
 * -- CREATE POLICY "Allow all operations" ON sync_logs FOR ALL USING (true);
 */
