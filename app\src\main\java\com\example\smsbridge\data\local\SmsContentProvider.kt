package com.example.smsbridge.data.local

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.Telephony
import android.util.Log
import com.example.smsbridge.data.model.SmsMessage
import com.example.smsbridge.data.model.SmsType
import com.example.smsbridge.data.model.SyncStatus
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for reading SMS messages from Android's SMS ContentProvider
 */
@Singleton
class SmsContentProvider @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "SmsContentProvider"
        
        // SMS ContentProvider columns
        private val SMS_PROJECTION = arrayOf(
            Telephony.Sms._ID,
            Telephony.Sms.ADDRESS,
            Telephony.Sms.BODY,
            Telephony.Sms.DATE,
            Telephony.Sms.TYPE,
            Telephony.Sms.READ,
            Telephony.Sms.THREAD_ID
        )
    }
    
    /**
     * Read all SMS messages from the device
     */
    suspend fun getAllSmsMessages(): List<SmsMessage> = withContext(Dispatchers.IO) {
        val messages = mutableListOf<SmsMessage>()
        
        try {
            val cursor = context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                SMS_PROJECTION,
                null,
                null,
                "${Telephony.Sms.DATE} DESC"
            )
            
            cursor?.use { c ->
                while (c.moveToNext()) {
                    val message = createSmsMessageFromCursor(c)
                    message?.let { messages.add(it) }
                }
            }
            
            Log.d(TAG, "Retrieved ${messages.size} SMS messages")
        } catch (e: Exception) {
            Log.e(TAG, "Error reading SMS messages", e)
        }
        
        return@withContext messages
    }
    
    /**
     * Read SMS messages after a specific timestamp
     */
    suspend fun getSmsMessagesAfter(timestamp: Long): List<SmsMessage> = withContext(Dispatchers.IO) {
        val messages = mutableListOf<SmsMessage>()
        
        try {
            val selection = "${Telephony.Sms.DATE} > ?"
            val selectionArgs = arrayOf(timestamp.toString())
            
            val cursor = context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                SMS_PROJECTION,
                selection,
                selectionArgs,
                "${Telephony.Sms.DATE} DESC"
            )
            
            cursor?.use { c ->
                while (c.moveToNext()) {
                    val message = createSmsMessageFromCursor(c)
                    message?.let { messages.add(it) }
                }
            }
            
            Log.d(TAG, "Retrieved ${messages.size} SMS messages after timestamp $timestamp")
        } catch (e: Exception) {
            Log.e(TAG, "Error reading SMS messages after timestamp", e)
        }
        
        return@withContext messages
    }
    
    /**
     * Read SMS messages from a specific phone number
     */
    suspend fun getSmsMessagesFromNumber(phoneNumber: String): List<SmsMessage> = withContext(Dispatchers.IO) {
        val messages = mutableListOf<SmsMessage>()
        
        try {
            val selection = "${Telephony.Sms.ADDRESS} = ?"
            val selectionArgs = arrayOf(phoneNumber)
            
            val cursor = context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                SMS_PROJECTION,
                selection,
                selectionArgs,
                "${Telephony.Sms.DATE} DESC"
            )
            
            cursor?.use { c ->
                while (c.moveToNext()) {
                    val message = createSmsMessageFromCursor(c)
                    message?.let { messages.add(it) }
                }
            }
            
            Log.d(TAG, "Retrieved ${messages.size} SMS messages from $phoneNumber")
        } catch (e: Exception) {
            Log.e(TAG, "Error reading SMS messages from number", e)
        }
        
        return@withContext messages
    }
    
    /**
     * Get the count of SMS messages
     */
    suspend fun getSmsMessageCount(): Int = withContext(Dispatchers.IO) {
        try {
            val cursor = context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                arrayOf("COUNT(*)"),
                null,
                null,
                null
            )
            
            cursor?.use { c ->
                if (c.moveToFirst()) {
                    return@withContext c.getInt(0)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting SMS message count", e)
        }
        
        return@withContext 0
    }
    
    /**
     * Create SmsMessage object from cursor
     */
    private fun createSmsMessageFromCursor(cursor: Cursor): SmsMessage? {
        return try {
            val id = cursor.getLong(cursor.getColumnIndexOrThrow(Telephony.Sms._ID))
            val address = cursor.getString(cursor.getColumnIndexOrThrow(Telephony.Sms.ADDRESS)) ?: ""
            val body = cursor.getString(cursor.getColumnIndexOrThrow(Telephony.Sms.BODY)) ?: ""
            val date = cursor.getLong(cursor.getColumnIndexOrThrow(Telephony.Sms.DATE))
            val type = cursor.getInt(cursor.getColumnIndexOrThrow(Telephony.Sms.TYPE))
            val read = cursor.getInt(cursor.getColumnIndexOrThrow(Telephony.Sms.READ)) == 1
            val threadId = cursor.getLong(cursor.getColumnIndexOrThrow(Telephony.Sms.THREAD_ID))
            
            SmsMessage(
                androidId = id,
                phoneNumber = address,
                content = body,
                timestamp = date,
                type = SmsType.fromValue(type),
                isRead = read,
                threadId = threadId,
                syncStatus = SyncStatus.PENDING
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error creating SMS message from cursor", e)
            null
        }
    }
}
