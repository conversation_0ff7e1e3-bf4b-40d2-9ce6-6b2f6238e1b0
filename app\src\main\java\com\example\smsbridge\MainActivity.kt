package com.example.smsbridge

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.smsbridge.ui.screen.main.MainScreen
import com.example.smsbridge.ui.screen.main.MainViewModel
import com.example.smsbridge.ui.screen.permissions.PermissionsScreen
import com.example.smsbridge.ui.screen.permissions.PermissionsViewModel
import com.example.smsbridge.ui.screen.settings.SettingsScreen
import com.example.smsbridge.ui.screen.settings.SettingsViewModel
import com.example.smsbridge.ui.theme.SMSBridgeTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            SMSBridgeTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()

                    NavHost(
                        navController = navController,
                        startDestination = "permissions"
                    ) {
                        composable("permissions") {
                            val viewModel: PermissionsViewModel = hiltViewModel()
                            PermissionsScreen(
                                viewModel = viewModel,
                                onPermissionsGranted = {
                                    navController.navigate("main") {
                                        popUpTo("permissions") { inclusive = true }
                                    }
                                }
                            )
                        }

                        composable("main") {
                            val viewModel: MainViewModel = hiltViewModel()
                            MainScreen(
                                viewModel = viewModel,
                                onNavigateToSettings = {
                                    navController.navigate("settings")
                                }
                            )
                        }

                        composable("settings") {
                            val viewModel: SettingsViewModel = hiltViewModel()
                            SettingsScreen(
                                viewModel = viewModel,
                                onNavigateBack = {
                                    navController.popBackStack()
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}