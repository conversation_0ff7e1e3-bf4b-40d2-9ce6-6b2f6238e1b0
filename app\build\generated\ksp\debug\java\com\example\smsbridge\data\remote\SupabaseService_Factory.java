package com.example.smsbridge.data.remote;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SupabaseService_Factory implements Factory<SupabaseService> {
  private final Provider<Context> contextProvider;

  private final Provider<SupabaseConfig> supabaseConfigProvider;

  public SupabaseService_Factory(Provider<Context> contextProvider,
      Provider<SupabaseConfig> supabaseConfigProvider) {
    this.contextProvider = contextProvider;
    this.supabaseConfigProvider = supabaseConfigProvider;
  }

  @Override
  public SupabaseService get() {
    return newInstance(contextProvider.get(), supabaseConfigProvider.get());
  }

  public static SupabaseService_Factory create(Provider<Context> contextProvider,
      Provider<SupabaseConfig> supabaseConfigProvider) {
    return new SupabaseService_Factory(contextProvider, supabaseConfigProvider);
  }

  public static SupabaseService newInstance(Context context, SupabaseConfig supabaseConfig) {
    return new SupabaseService(context, supabaseConfig);
  }
}
