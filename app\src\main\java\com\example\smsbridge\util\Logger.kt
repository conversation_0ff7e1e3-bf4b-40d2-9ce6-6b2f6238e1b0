package com.example.smsbridge.util

import android.util.Log
import com.example.smsbridge.BuildConfig

/**
 * Centralized logging utility for SMS Bridge app
 */
object Logger {
    
    private const val TAG_PREFIX = "SMSBridge"
    
    fun d(tag: String, message: String) {
        if (BuildConfig.DEBUG) {
            Log.d("$TAG_PREFIX:$tag", message)
        }
    }
    
    fun i(tag: String, message: String) {
        Log.i("$TAG_PREFIX:$tag", message)
    }
    
    fun w(tag: String, message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            Log.w("$TAG_PREFIX:$tag", message, throwable)
        } else {
            Log.w("$TAG_PREFIX:$tag", message)
        }
    }
    
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            Log.e("$TAG_PREFIX:$tag", message, throwable)
        } else {
            Log.e("$TAG_PREFIX:$tag", message)
        }
    }
    
    fun v(tag: String, message: String) {
        if (BuildConfig.DEBUG) {
            Log.v("$TAG_PREFIX:$tag", message)
        }
    }
}

/**
 * Extension functions for easier logging
 */
inline fun <reified T> T.logD(message: String) {
    Logger.d(T::class.java.simpleName, message)
}

inline fun <reified T> T.logI(message: String) {
    Logger.i(T::class.java.simpleName, message)
}

inline fun <reified T> T.logW(message: String, throwable: Throwable? = null) {
    Logger.w(T::class.java.simpleName, message, throwable)
}

inline fun <reified T> T.logE(message: String, throwable: Throwable? = null) {
    Logger.e(T::class.java.simpleName, message, throwable)
}

inline fun <reified T> T.logV(message: String) {
    Logger.v(T::class.java.simpleName, message)
}
