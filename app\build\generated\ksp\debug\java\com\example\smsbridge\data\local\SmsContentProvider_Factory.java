package com.example.smsbridge.data.local;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SmsContentProvider_Factory implements Factory<SmsContentProvider> {
  private final Provider<Context> contextProvider;

  public SmsContentProvider_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SmsContentProvider get() {
    return newInstance(contextProvider.get());
  }

  public static SmsContentProvider_Factory create(Provider<Context> contextProvider) {
    return new SmsContentProvider_Factory(contextProvider);
  }

  public static SmsContentProvider newInstance(Context context) {
    return new SmsContentProvider(context);
  }
}
