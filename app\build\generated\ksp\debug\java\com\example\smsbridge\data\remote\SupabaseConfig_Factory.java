package com.example.smsbridge.data.remote;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SupabaseConfig_Factory implements Factory<SupabaseConfig> {
  @Override
  public SupabaseConfig get() {
    return newInstance();
  }

  public static SupabaseConfig_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SupabaseConfig newInstance() {
    return new SupabaseConfig();
  }

  private static final class InstanceHolder {
    private static final SupabaseConfig_Factory INSTANCE = new SupabaseConfig_Factory();
  }
}
