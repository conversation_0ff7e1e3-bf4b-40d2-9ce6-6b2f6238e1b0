# SMS Bridge Android App

SMS Bridge is an Android application that automatically syncs your SMS messages to a Supabase database, providing secure cloud backup and cross-device access to your messages.

## Features

- **Real-time SMS Sync**: Automatically sync incoming SMS messages as they arrive
- **Background Sync**: Periodic background synchronization of all SMS messages
- **Secure Storage**: Messages are stored in your personal Supabase database
- **Permission Management**: Clear explanations and proper handling of SMS permissions
- **Configurable Settings**: Control sync frequency, WiFi-only mode, and more
- **Modern UI**: Built with Jetpack Compose for a smooth user experience

## Prerequisites

- Android device running Android 7.0 (API level 24) or higher
- Supabase account and project
- Android Studio (for development)

## Supabase Setup

### 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com) and create a new account or sign in
2. Create a new project
3. Wait for the project to be fully set up

### 2. Set Up the Database Schema

1. In your Supabase dashboard, go to the SQL Editor
2. Run the following SQL to create the required tables:

```sql
-- Create SMS messages table
CREATE TABLE IF NOT EXISTS sms_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    android_id BIGINT,
    phone_number TEXT NOT NULL,
    content TEXT NOT NULL,
    timestamp BIGINT NOT NULL,
    type TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    thread_id BIGINT,
    device_id TEXT,
    sync_status TEXT DEFAULT 'PENDING',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sms_messages_phone_number ON sms_messages(phone_number);
CREATE INDEX IF NOT EXISTS idx_sms_messages_timestamp ON sms_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_sms_messages_device_id ON sms_messages(device_id);
CREATE INDEX IF NOT EXISTS idx_sms_messages_sync_status ON sms_messages(sync_status);

-- Create sync logs table for tracking sync operations
CREATE TABLE IF NOT EXISTS sync_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    device_id TEXT NOT NULL,
    sync_type TEXT NOT NULL,
    messages_processed INTEGER DEFAULT 0,
    messages_successful INTEGER DEFAULT 0,
    messages_failed INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'RUNNING',
    error_message TEXT
);

-- Create index for sync logs
CREATE INDEX IF NOT EXISTS idx_sync_logs_device_id ON sync_logs(device_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_started_at ON sync_logs(started_at);

-- Enable Row Level Security (RLS)
ALTER TABLE sms_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated access
CREATE POLICY "Allow all operations for authenticated users" ON sms_messages
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON sync_logs
    FOR ALL USING (auth.role() = 'authenticated');
```

### 3. Get Your Supabase Credentials

1. In your Supabase dashboard, go to Settings → API
2. Copy your Project URL (looks like: `https://your-project-id.supabase.co`)
3. Copy your `anon` `public` API key

## Android App Setup

### 1. Clone and Build

```bash
git clone <repository-url>
cd SMSBridge
./gradlew build
```

### 2. Configure Supabase Connection

You have two options to configure the Supabase connection:

#### Option A: Through the App (Recommended)
1. Install and launch the app
2. Grant the required permissions
3. Go to Settings
4. Enter your Supabase URL and API key
5. Save the configuration

#### Option B: Hardcode in Source (Development Only)
1. Open `app/src/main/java/com/example/smsbridge/data/remote/SupabaseConfig.kt`
2. Replace the placeholder values:
```kotlin
private const val SUPABASE_URL = "https://your-project-id.supabase.co"
private const val SUPABASE_ANON_KEY = "your-anon-key-here"
```

### 3. Install and Run

1. Connect your Android device or start an emulator
2. Run the app:
```bash
./gradlew installDebug
```

## App Usage

### First Launch

1. **Permissions**: The app will request SMS-related permissions with clear explanations
2. **Configuration**: Set up your Supabase connection in the Settings screen
3. **Initial Sync**: Perform a full sync to upload existing messages

### Daily Usage

- **Automatic Sync**: New SMS messages are automatically synced when received
- **Manual Sync**: Use the refresh button to manually sync messages
- **Settings**: Adjust sync frequency, WiFi-only mode, and other preferences

### Settings Options

- **Auto Sync**: Enable/disable automatic background synchronization
- **Real-time Sync**: Sync new messages immediately when received
- **WiFi Only**: Only sync when connected to WiFi
- **Sync Frequency**: How often to perform background sync (1-72 hours)

## Permissions Required

The app requires the following permissions:

- **READ_SMS**: To read existing SMS messages from your device
- **RECEIVE_SMS**: To receive new SMS messages in real-time
- **READ_PHONE_STATE**: To identify your device for proper message organization
- **INTERNET**: To communicate with Supabase
- **ACCESS_NETWORK_STATE**: To check network connectivity
- **WAKE_LOCK**: For background sync operations
- **FOREGROUND_SERVICE**: For reliable background sync

## Security and Privacy

- **Local Storage**: No SMS messages are stored locally in the app
- **Encryption**: All communication with Supabase uses HTTPS
- **Personal Database**: Messages are stored in your personal Supabase database
- **No Third Parties**: No data is shared with third parties
- **Open Source**: Full source code is available for review

## Troubleshooting

### Common Issues

1. **Sync Not Working**
   - Check internet connection
   - Verify Supabase configuration
   - Ensure all permissions are granted
   - Check if auto-sync is enabled in settings

2. **Permission Denied**
   - Go to Android Settings → Apps → SMS Bridge → Permissions
   - Enable all required permissions
   - Restart the app

3. **Supabase Connection Error**
   - Verify your Supabase URL and API key
   - Check if your Supabase project is active
   - Ensure RLS policies are set up correctly

### Logs

The app uses structured logging. To view logs:
```bash
adb logcat | grep SMSBridge
```

## Development

### Architecture

- **MVVM**: Model-View-ViewModel architecture with Jetpack Compose
- **Dependency Injection**: Hilt for dependency management
- **Background Work**: WorkManager for reliable background sync
- **Data Storage**: DataStore for app preferences
- **Networking**: Ktor client with Supabase SDK

### Key Components

- `SmsContentProvider`: Reads SMS messages from Android's ContentProvider
- `SmsReceiver`: BroadcastReceiver for real-time SMS interception
- `SupabaseService`: Handles all Supabase database operations
- `SmsSyncWorker`: WorkManager worker for background sync
- `PermissionManager`: Manages SMS-related permissions

### Building for Release

1. Update version in `app/build.gradle.kts`
2. Generate signed APK:
```bash
./gradlew assembleRelease
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Search existing GitHub issues
3. Create a new issue with detailed information

## Changelog

### Version 1.0.0
- Initial release
- Real-time SMS sync
- Background sync with WorkManager
- Supabase integration
- Modern Compose UI
- Comprehensive permission management
