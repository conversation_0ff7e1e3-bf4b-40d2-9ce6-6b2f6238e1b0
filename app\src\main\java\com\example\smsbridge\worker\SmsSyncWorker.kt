package com.example.smsbridge.worker

import android.content.Context
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.smsbridge.data.local.SmsContentProvider
import com.example.smsbridge.data.model.SmsMessage
import com.example.smsbridge.data.model.SmsType
import com.example.smsbridge.data.model.SyncStatus
import com.example.smsbridge.data.remote.SupabaseService
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject

/**
 * WorkManager worker for syncing SMS messages to Supabase
 */
@HiltWorker
class SmsSyncWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val supabaseService: SupabaseService,
    private val smsContentProvider: SmsContentProvider
) : CoroutineWorker(context, workerParams) {
    
    companion object {
        private const val TAG = "SmsSyncWorker"
        
        // Work tags
        const val WORK_TAG_FULL_SYNC = "full_sync"
        const val WORK_TAG_INCREMENTAL_SYNC = "incremental_sync"
        const val WORK_TAG_REAL_TIME_SYNC = "real_time_sync"
        
        // Input data keys
        const val KEY_SYNC_TYPE = "sync_type"
        const val KEY_PHONE_NUMBER = "phone_number"
        const val KEY_CONTENT = "content"
        const val KEY_TIMESTAMP = "timestamp"
        const val KEY_TYPE = "type"
        const val KEY_IS_READ = "is_read"
        const val KEY_LAST_SYNC_TIMESTAMP = "last_sync_timestamp"
        
        // Sync types
        const val SYNC_TYPE_FULL = "full"
        const val SYNC_TYPE_INCREMENTAL = "incremental"
        const val SYNC_TYPE_REAL_TIME = "real_time"
    }
    
    override suspend fun doWork(): Result {
        Log.d(TAG, "Starting SMS sync work")
        
        return try {
            val syncType = inputData.getString(KEY_SYNC_TYPE) ?: SYNC_TYPE_FULL
            
            when (syncType) {
                SYNC_TYPE_FULL -> performFullSync()
                SYNC_TYPE_INCREMENTAL -> performIncrementalSync()
                SYNC_TYPE_REAL_TIME -> performRealTimeSync()
                else -> {
                    Log.w(TAG, "Unknown sync type: $syncType")
                    Result.failure()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during SMS sync", e)
            Result.failure()
        }
    }
    
    /**
     * Perform full sync of all SMS messages
     */
    private suspend fun performFullSync(): Result {
        Log.d(TAG, "Performing full SMS sync")
        
        return try {
            val allMessages = smsContentProvider.getAllSmsMessages()
            Log.d(TAG, "Found ${allMessages.size} SMS messages to sync")
            
            if (allMessages.isEmpty()) {
                return Result.success()
            }
            
            val result = supabaseService.saveSmsMessages(allMessages)
            
            result.fold(
                onSuccess = { savedMessages ->
                    Log.d(TAG, "Successfully synced ${savedMessages.size} messages")
                    Result.success()
                },
                onFailure = { error ->
                    Log.e(TAG, "Failed to sync messages", error)
                    Result.retry()
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error during full sync", e)
            Result.failure()
        }
    }
    
    /**
     * Perform incremental sync of messages after last sync timestamp
     */
    private suspend fun performIncrementalSync(): Result {
        Log.d(TAG, "Performing incremental SMS sync")
        
        return try {
            val lastSyncTimestamp = inputData.getLong(KEY_LAST_SYNC_TIMESTAMP, 0L)
            val newMessages = smsContentProvider.getSmsMessagesAfter(lastSyncTimestamp)
            
            Log.d(TAG, "Found ${newMessages.size} new SMS messages to sync")
            
            if (newMessages.isEmpty()) {
                return Result.success()
            }
            
            val result = supabaseService.saveSmsMessages(newMessages)
            
            result.fold(
                onSuccess = { savedMessages ->
                    Log.d(TAG, "Successfully synced ${savedMessages.size} new messages")
                    Result.success()
                },
                onFailure = { error ->
                    Log.e(TAG, "Failed to sync new messages", error)
                    Result.retry()
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error during incremental sync", e)
            Result.failure()
        }
    }
    
    /**
     * Perform real-time sync of a single SMS message
     */
    private suspend fun performRealTimeSync(): Result {
        Log.d(TAG, "Performing real-time SMS sync")
        
        return try {
            val phoneNumber = inputData.getString(KEY_PHONE_NUMBER) ?: return Result.failure()
            val content = inputData.getString(KEY_CONTENT) ?: return Result.failure()
            val timestamp = inputData.getLong(KEY_TIMESTAMP, System.currentTimeMillis())
            val typeString = inputData.getString(KEY_TYPE) ?: SmsType.INBOX.name
            val isRead = inputData.getBoolean(KEY_IS_READ, false)
            
            val message = SmsMessage(
                phoneNumber = phoneNumber,
                content = content,
                timestamp = timestamp,
                type = SmsType.valueOf(typeString),
                isRead = isRead,
                syncStatus = SyncStatus.PENDING
            )
            
            val result = supabaseService.saveSmsMessage(message)
            
            result.fold(
                onSuccess = { savedMessage ->
                    Log.d(TAG, "Successfully synced real-time message: ${savedMessage.id}")
                    Result.success()
                },
                onFailure = { error ->
                    Log.e(TAG, "Failed to sync real-time message", error)
                    Result.retry()
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error during real-time sync", e)
            Result.failure()
        }
    }
}
