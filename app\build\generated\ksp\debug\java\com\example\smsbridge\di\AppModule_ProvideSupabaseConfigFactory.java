package com.example.smsbridge.di;

import com.example.smsbridge.data.remote.SupabaseConfig;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideSupabaseConfigFactory implements Factory<SupabaseConfig> {
  @Override
  public SupabaseConfig get() {
    return provideSupabaseConfig();
  }

  public static AppModule_ProvideSupabaseConfigFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SupabaseConfig provideSupabaseConfig() {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideSupabaseConfig());
  }

  private static final class InstanceHolder {
    private static final AppModule_ProvideSupabaseConfigFactory INSTANCE = new AppModule_ProvideSupabaseConfigFactory();
  }
}
