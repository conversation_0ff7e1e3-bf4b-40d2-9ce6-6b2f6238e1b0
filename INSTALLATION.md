# SMS Bridge Installation Guide

This guide will help you set up and install the SMS Bridge Android application.

## Prerequisites

- Android device running Android 7.0 (API level 24) or higher
- Android Studio (for building from source)
- Supabase account (free tier available)
- Basic understanding of Android permissions

## Quick Start

### Option 1: Install Pre-built APK (Recommended for Users)

1. Download the latest APK from the releases page
2. Enable "Install from unknown sources" in your Android settings
3. Install the APK
4. Follow the [Supabase Setup Guide](SUPABASE_SETUP.md)
5. Launch the app and configure your Supabase connection

### Option 2: Build from Source (For Developers)

Follow the detailed instructions below.

## Detailed Installation Steps

### Step 1: Set Up Supabase

Before installing the app, you need to set up your Supabase database:

1. **Create Supabase Account**: Go to [supabase.com](https://supabase.com) and create an account
2. **Create New Project**: Set up a new project in Supabase
3. **Set Up Database**: Follow the [Supabase Setup Guide](SUPABASE_SETUP.md) to create the required tables
4. **Get Credentials**: Note down your Project URL and anon API key

### Step 2: Clone the Repository

```bash
git clone https://github.com/your-username/SMSBridge.git
cd SMSBridge
```

### Step 3: Configure Supabase Connection

You have several options to configure your Supabase connection:

#### Option A: Using local.properties (Recommended for Development)

1. Copy the example file:
```bash
cp local.properties.example local.properties
```

2. Edit `local.properties` and add your Supabase credentials:
```properties
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
```

#### Option B: Using BuildConfig (Alternative)

Edit `app/build.gradle.kts` and update the buildConfigField values:
```kotlin
buildConfigField("String", "SUPABASE_URL", "\"https://your-project-id.supabase.co\"")
buildConfigField("String", "SUPABASE_ANON_KEY", "\"your-anon-key-here\"")
```

#### Option C: Configure in App (Runtime Configuration)

Leave the default values and configure through the app's settings screen after installation.

### Step 4: Build the App

1. **Open in Android Studio**:
   - Launch Android Studio
   - Open the project folder
   - Wait for Gradle sync to complete

2. **Build the Project**:
   ```bash
   ./gradlew build
   ```

3. **Install on Device**:
   - Connect your Android device via USB
   - Enable USB debugging in Developer Options
   - Run:
   ```bash
   ./gradlew installDebug
   ```

### Step 5: First Launch Setup

1. **Launch the App**: Find "SMS Bridge" in your app drawer and launch it

2. **Grant Permissions**: The app will request the following permissions:
   - **Read SMS**: To access your existing messages
   - **Receive SMS**: To sync new messages in real-time
   - **Read Phone State**: To identify your device

3. **Configure Supabase** (if not done in build):
   - Go to Settings
   - Enter your Supabase URL and API key
   - Save the configuration

4. **Initial Sync**: 
   - Tap the "Full Sync" button to upload existing messages
   - This may take a few minutes depending on your message count

## Verification

### Test the Installation

1. **Check Sync Status**: The main screen should show your message statistics
2. **Send Test SMS**: Send yourself an SMS and verify it appears in your Supabase database
3. **Check Settings**: Ensure all settings are configured correctly

### Verify Supabase Connection

1. Open your Supabase dashboard
2. Go to Table Editor → sms_messages
3. You should see your synced messages

## Troubleshooting

### Common Installation Issues

#### Build Errors

**"Could not resolve dependencies"**
- Ensure you have a stable internet connection
- Try: `./gradlew clean build`

**"SDK not found"**
- Update `local.properties` with correct SDK path
- Or set `ANDROID_HOME` environment variable

#### Runtime Issues

**"Supabase not configured"**
- Verify your URL and API key are correct
- Check that your Supabase project is active
- Ensure the database tables are created

**"Permission denied"**
- Go to Android Settings → Apps → SMS Bridge → Permissions
- Enable all required permissions
- Restart the app

**"Sync not working"**
- Check internet connection
- Verify Supabase configuration
- Check app logs: `adb logcat | grep SMSBridge`

### Getting Help

1. **Check Logs**: Use `adb logcat | grep SMSBridge` to see detailed logs
2. **Supabase Dashboard**: Check for errors in your Supabase project logs
3. **GitHub Issues**: Search existing issues or create a new one
4. **Documentation**: Review the [README](README.md) and [Supabase Setup Guide](SUPABASE_SETUP.md)

## Security Considerations

### Before Installation

1. **Review Permissions**: Understand what each permission does
2. **Secure Credentials**: Never commit API keys to version control
3. **Network Security**: Ensure you're using HTTPS URLs

### After Installation

1. **Regular Updates**: Keep the app updated
2. **Monitor Usage**: Check your Supabase usage dashboard
3. **Backup**: Consider backing up your Supabase database

## Uninstallation

To completely remove SMS Bridge:

1. **Uninstall App**: Remove from Android settings or app drawer
2. **Clean Supabase**: Optionally delete the sms_messages table in Supabase
3. **Revoke Permissions**: Permissions are automatically revoked on uninstall

## Next Steps

After successful installation:

1. **Configure Sync Settings**: Adjust sync frequency and WiFi-only mode
2. **Enable Auto-Sync**: Turn on automatic background synchronization  
3. **Test Real-time Sync**: Send yourself messages to verify real-time sync
4. **Explore Features**: Try manual sync, view sync statistics, etc.

## Development Setup

For developers who want to contribute:

1. **Fork Repository**: Create your own fork on GitHub
2. **Set Up IDE**: Configure Android Studio with proper code style
3. **Run Tests**: Execute `./gradlew test` to run unit tests
4. **Debug Build**: Use debug build variants for development
5. **Contribute**: Submit pull requests with your improvements

## Support

- **Documentation**: [README.md](README.md)
- **Supabase Setup**: [SUPABASE_SETUP.md](SUPABASE_SETUP.md)
- **Issues**: GitHub Issues page
- **Discussions**: GitHub Discussions for questions

---

**Note**: This app requires SMS permissions and internet access. Your messages are stored in your personal Supabase database and are not shared with third parties.
