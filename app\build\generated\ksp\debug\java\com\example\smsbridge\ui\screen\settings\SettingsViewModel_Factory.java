package com.example.smsbridge.ui.screen.settings;

import com.example.smsbridge.data.preferences.PreferencesManager;
import com.example.smsbridge.data.remote.SupabaseConfig;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsViewModel_Factory implements Factory<SettingsViewModel> {
  private final Provider<PreferencesManager> preferencesManagerProvider;

  private final Provider<SupabaseConfig> supabaseConfigProvider;

  public SettingsViewModel_Factory(Provider<PreferencesManager> preferencesManagerProvider,
      Provider<SupabaseConfig> supabaseConfigProvider) {
    this.preferencesManagerProvider = preferencesManagerProvider;
    this.supabaseConfigProvider = supabaseConfigProvider;
  }

  @Override
  public SettingsViewModel get() {
    return newInstance(preferencesManagerProvider.get(), supabaseConfigProvider.get());
  }

  public static SettingsViewModel_Factory create(
      Provider<PreferencesManager> preferencesManagerProvider,
      Provider<SupabaseConfig> supabaseConfigProvider) {
    return new SettingsViewModel_Factory(preferencesManagerProvider, supabaseConfigProvider);
  }

  public static SettingsViewModel newInstance(PreferencesManager preferencesManager,
      SupabaseConfig supabaseConfig) {
    return new SettingsViewModel(preferencesManager, supabaseConfig);
  }
}
