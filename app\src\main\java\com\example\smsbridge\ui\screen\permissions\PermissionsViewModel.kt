package com.example.smsbridge.ui.screen.permissions

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.smsbridge.data.preferences.PreferencesManager
import com.example.smsbridge.util.PermissionManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class PermissionState(
    val permission: String,
    val displayName: String,
    val explanation: String,
    val isGranted: Boolean
)

data class PermissionsUiState(
    val permissionStates: List<PermissionState> = emptyList(),
    val allPermissionsGranted: Boolean = false,
    val isLoading: Boolean = false
)

@HiltViewModel
class PermissionsViewModel @Inject constructor(
    private val permissionManager: PermissionManager,
    private val preferencesManager: PreferencesManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(PermissionsUiState())
    val uiState: StateFlow<PermissionsUiState> = _uiState.asStateFlow()
    
    init {
        checkPermissions()
    }
    
    private fun checkPermissions() {
        val permissionStates = PermissionManager.REQUIRED_PERMISSIONS.map { permission ->
            PermissionState(
                permission = permission,
                displayName = permissionManager.getPermissionDisplayName(permission),
                explanation = permissionManager.getPermissionExplanation(permission),
                isGranted = permissionManager.isPermissionGranted(permission)
            )
        }
        
        val allGranted = permissionStates.all { it.isGranted }
        
        _uiState.value = _uiState.value.copy(
            permissionStates = permissionStates,
            allPermissionsGranted = allGranted
        )
        
        if (allGranted) {
            markPermissionsExplained()
        }
    }
    
    fun onPermissionsResult(permissions: Map<String, Boolean>) {
        checkPermissions()
    }
    
    private fun markPermissionsExplained() {
        viewModelScope.launch {
            preferencesManager.setPermissionsExplained(true)
        }
    }
}
