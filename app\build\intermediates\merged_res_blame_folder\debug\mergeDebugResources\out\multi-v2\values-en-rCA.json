{"logs": [{"outputFile": "com.example.smsbridge.app-mergeDebugResources-65:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\569a4848e6cd56af57d5651d94321d08\\transformed\\material3-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2467,2588,2704,2827,2952,3044,3142,3259,3383,3480,3582,3684,3814,3953,4059,4158,4234,4330,4424,4528,4615,4702,4804,4884,4968,5069,5170,5270,5369,5457,5563,5664,5768,5884,5964,6064", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,103,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2462,2583,2699,2822,2947,3039,3137,3254,3378,3475,3577,3679,3809,3948,4054,4153,4229,4325,4419,4523,4610,4697,4799,4879,4963,5064,5165,5265,5364,5452,5558,5659,5763,5879,5959,6059,6154"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1851,1969,2085,2196,2310,2409,2504,2616,2752,2868,3004,3088,3187,3278,3375,3494,3619,3723,3850,3973,4101,4263,4384,4500,4623,4748,4840,4938,5055,5179,5276,5378,5480,5610,5749,5855,5954,6030,6126,6220,6324,6411,6498,6600,6680,6764,6865,6966,7066,7165,7253,7359,7460,7564,7680,7760,7860", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,103,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "1964,2080,2191,2305,2404,2499,2611,2747,2863,2999,3083,3182,3273,3370,3489,3614,3718,3845,3968,4096,4258,4379,4495,4618,4743,4835,4933,5050,5174,5271,5373,5475,5605,5744,5850,5949,6025,6121,6215,6319,6406,6493,6595,6675,6759,6860,6961,7061,7160,7248,7354,7455,7559,7675,7755,7855,7950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79a66074d77b36bc85e223e1c4ed5168\\transformed\\foundation-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "89,90", "startColumns": "4,4", "startOffsets": "8943,9029", "endColumns": "85,84", "endOffsets": "9024,9109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd357763fc5d12aea42dedd83b4a8eb5\\transformed\\browser-1.8.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "997,1374,1471,1580", "endColumns": "97,96,108,98", "endOffsets": "1090,1466,1575,1674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f116294e462fcef23dad22a63f303a87\\transformed\\core-1.13.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "2,3,4,5,6,7,8,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,501,605,707,8579", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "196,298,397,496,600,702,818,8675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\633a39a9b9a3ac22f07ac3578025771c\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,558,640,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,75,71,69,76,65,119", "endOffsets": "192,274,368,467,553,635,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1612"}, "to": {"startLines": "9,10,12,13,14,18,19,77,78,79,80,81,82,83,84,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "823,915,1095,1189,1288,1679,1761,7955,8044,8128,8206,8288,8361,8437,8509,8680,8757,8823", "endColumns": "91,81,93,98,85,81,89,88,83,77,81,72,75,71,69,76,65,119", "endOffsets": "910,992,1184,1283,1369,1756,1846,8039,8123,8201,8283,8356,8432,8504,8574,8752,8818,8938"}}]}]}