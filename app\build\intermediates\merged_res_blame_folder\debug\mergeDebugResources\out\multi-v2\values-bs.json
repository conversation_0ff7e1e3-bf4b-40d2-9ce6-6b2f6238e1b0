{"logs": [{"outputFile": "com.example.smsbridge.app-mergeDebugResources-65:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\633a39a9b9a3ac22f07ac3578025771c\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,1000,1086,1159,1236,1315,1392,1472,1542", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,995,1081,1154,1231,1310,1387,1467,1537,1655"}, "to": {"startLines": "9,10,12,13,14,18,19,77,78,79,80,81,82,83,84,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "830,931,1123,1217,1316,1716,1793,8227,8319,8404,8485,8571,8644,8721,8800,8978,9058,9128", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "926,1014,1212,1311,1397,1788,1880,8314,8399,8480,8566,8639,8716,8795,8872,9053,9123,9241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\79a66074d77b36bc85e223e1c4ed5168\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "89,90", "startColumns": "4,4", "startOffsets": "9246,9336", "endColumns": "89,91", "endOffsets": "9331,9423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd357763fc5d12aea42dedd83b4a8eb5\\transformed\\browser-1.8.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,99", "endOffsets": "154,254,368,468"}, "to": {"startLines": "11,15,16,17", "startColumns": "4,4,4,4", "startOffsets": "1019,1402,1502,1616", "endColumns": "103,99,113,99", "endOffsets": "1118,1497,1611,1711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f116294e462fcef23dad22a63f303a87\\transformed\\core-1.13.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "2,3,4,5,6,7,8,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,403,507,611,713,8877", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "198,300,398,502,606,708,825,8973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\569a4848e6cd56af57d5651d94321d08\\transformed\\material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4711,4805,4896,5005,5093,5176,5273,5377,5470,5567,5655,5763,5860,5962,6100,6190,6298", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4706,4800,4891,5000,5088,5171,5268,5372,5465,5562,5650,5758,5855,5957,6095,6185,6293,6392"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1885,2004,2124,2245,2365,2464,2562,2677,2822,2942,3080,3165,3265,3358,3456,3573,3700,3805,3940,4074,4215,4385,4520,4643,4770,4898,4992,5090,5211,5339,5436,5539,5648,5787,5932,6041,6141,6226,6319,6414,6541,6635,6726,6835,6923,7006,7103,7207,7300,7397,7485,7593,7690,7792,7930,8020,8128", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "1999,2119,2240,2360,2459,2557,2672,2817,2937,3075,3160,3260,3353,3451,3568,3695,3800,3935,4069,4210,4380,4515,4638,4765,4893,4987,5085,5206,5334,5431,5534,5643,5782,5927,6036,6136,6221,6314,6409,6536,6630,6721,6830,6918,7001,7098,7202,7295,7392,7480,7588,7685,7787,7925,8015,8123,8222"}}]}]}