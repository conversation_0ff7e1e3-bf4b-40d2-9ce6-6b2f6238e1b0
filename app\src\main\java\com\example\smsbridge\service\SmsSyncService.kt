package com.example.smsbridge.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.example.smsbridge.R
import com.example.smsbridge.util.logD
import com.example.smsbridge.util.logE
import com.example.smsbridge.util.logI
import dagger.hilt.android.AndroidEntryPoint

/**
 * Foreground service for SMS synchronization
 */
@AndroidEntryPoint
class SmsSyncService : Service() {
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "sms_sync_channel"
        private const val CHANNEL_NAME = "SMS Sync"
        
        const val ACTION_START_SYNC = "com.example.smsbridge.START_SYNC"
        const val ACTION_STOP_SYNC = "com.example.smsbridge.STOP_SYNC"
        
        const val EXTRA_SYNC_TYPE = "sync_type"
        const val SYNC_TYPE_FULL = "full"
        const val SYNC_TYPE_INCREMENTAL = "incremental"
    }
    
    private var isRunning = false
    
    override fun onCreate() {
        super.onCreate()
        logD("SmsSyncService created")
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        logI("SmsSyncService onStartCommand: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_SYNC -> {
                val syncType = intent.getStringExtra(EXTRA_SYNC_TYPE) ?: SYNC_TYPE_INCREMENTAL
                startSync(syncType)
            }
            ACTION_STOP_SYNC -> {
                stopSync()
            }
        }
        
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun startSync(syncType: String) {
        if (isRunning) {
            logD("Sync already running, ignoring start request")
            return
        }
        
        isRunning = true
        logI("Starting SMS sync service with type: $syncType")
        
        val notification = createNotification("Syncing SMS messages...")
        startForeground(NOTIFICATION_ID, notification)
        
        // The actual sync work is handled by WorkManager
        // This service just provides the foreground notification
        
        // Auto-stop after a reasonable time (WorkManager will handle the actual work)
        android.os.Handler(mainLooper).postDelayed({
            stopSync()
        }, 30000) // 30 seconds
    }
    
    private fun stopSync() {
        if (!isRunning) {
            return
        }
        
        logI("Stopping SMS sync service")
        isRunning = false
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Notifications for SMS synchronization"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(contentText: String): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("SMS Bridge")
            .setContentText(contentText)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        logD("SmsSyncService destroyed")
        isRunning = false
    }
}
