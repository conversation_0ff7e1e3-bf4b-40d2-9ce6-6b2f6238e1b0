package com.example.smsbridge.data.remote

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import com.example.smsbridge.data.model.SmsMessage
import com.example.smsbridge.data.model.SmsMessageDto
import com.example.smsbridge.data.model.SyncStatus
import com.example.smsbridge.data.model.toDomain
import com.example.smsbridge.data.model.toDto
import dagger.hilt.android.qualifiers.ApplicationContext
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Columns
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for interacting with Supabase database
 */
@Singleton
class SupabaseService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val supabaseConfig: SupabaseConfig
) {
    
    companion object {
        private const val TAG = "SupabaseService"
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val RETRY_DELAY_MS = 1000L
        private const val BATCH_SIZE = 50
    }
    
    /**
     * Check if network is available
     */
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        
        return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
               capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
               capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
    }
    
    /**
     * Save a single SMS message to Supabase
     */
    suspend fun saveSmsMessage(message: SmsMessage): Result<SmsMessage> = withContext(Dispatchers.IO) {
        if (!supabaseConfig.isConfigured()) {
            return@withContext Result.failure(Exception("Supabase is not configured"))
        }
        
        if (!isNetworkAvailable()) {
            return@withContext Result.failure(Exception("No network connection available"))
        }
        
        return@withContext executeWithRetry {
            try {
                val messageDto = message.toDto()
                
                val response = supabaseConfig.client
                    .from(SupabaseConfig.SMS_MESSAGES_TABLE)
                    .insert(messageDto)
                    .decodeSingle<SmsMessageDto>()
                
                val savedMessage = response.toDomain().copy(syncStatus = SyncStatus.SYNCED)
                Log.d(TAG, "Successfully saved SMS message: ${savedMessage.id}")
                
                Result.success(savedMessage)
            } catch (e: Exception) {
                Log.e(TAG, "Error saving SMS message", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Save multiple SMS messages in batches
     */
    suspend fun saveSmsMessages(messages: List<SmsMessage>): Result<List<SmsMessage>> = withContext(Dispatchers.IO) {
        if (!supabaseConfig.isConfigured()) {
            return@withContext Result.failure(Exception("Supabase is not configured"))
        }
        
        if (!isNetworkAvailable()) {
            return@withContext Result.failure(Exception("No network connection available"))
        }
        
        val savedMessages = mutableListOf<SmsMessage>()
        val failedMessages = mutableListOf<SmsMessage>()
        
        // Process messages in batches
        messages.chunked(BATCH_SIZE).forEach { batch ->
            val result = executeWithRetry {
                try {
                    val messageDtos = batch.map { it.toDto() }
                    
                    val response = supabaseConfig.client
                        .from(SupabaseConfig.SMS_MESSAGES_TABLE)
                        .insert(messageDtos)
                        .decodeList<SmsMessageDto>()
                    
                    val batchSavedMessages = response.map { it.toDomain().copy(syncStatus = SyncStatus.SYNCED) }
                    Log.d(TAG, "Successfully saved batch of ${batchSavedMessages.size} SMS messages")
                    
                    Result.success(batchSavedMessages)
                } catch (e: Exception) {
                    Log.e(TAG, "Error saving SMS message batch", e)
                    Result.failure(e)
                }
            }
            
            result.fold(
                onSuccess = { savedMessages.addAll(it) },
                onFailure = { failedMessages.addAll(batch) }
            )
        }
        
        return@withContext if (failedMessages.isEmpty()) {
            Result.success(savedMessages)
        } else {
            Result.failure(Exception("Failed to save ${failedMessages.size} messages"))
        }
    }
    
    /**
     * Retrieve SMS messages from Supabase
     */
    suspend fun getSmsMessages(limit: Int = 100, offset: Int = 0): Result<List<SmsMessage>> = withContext(Dispatchers.IO) {
        if (!supabaseConfig.isConfigured()) {
            return@withContext Result.failure(Exception("Supabase is not configured"))
        }
        
        if (!isNetworkAvailable()) {
            return@withContext Result.failure(Exception("No network connection available"))
        }
        
        return@withContext executeWithRetry {
            try {
                val response = supabaseConfig.client
                    .from(SupabaseConfig.SMS_MESSAGES_TABLE)
                    .select(columns = Columns.ALL)
                    .limit(limit.toLong())
                    .range(offset.toLong(), (offset + limit - 1).toLong())
                    .order("timestamp", ascending = false)
                    .decodeList<SmsMessageDto>()
                
                val messages = response.map { it.toDomain() }
                Log.d(TAG, "Retrieved ${messages.size} SMS messages from Supabase")
                
                Result.success(messages)
            } catch (e: Exception) {
                Log.e(TAG, "Error retrieving SMS messages", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Get SMS messages after a specific timestamp
     */
    suspend fun getSmsMessagesAfter(timestamp: Long): Result<List<SmsMessage>> = withContext(Dispatchers.IO) {
        if (!supabaseConfig.isConfigured()) {
            return@withContext Result.failure(Exception("Supabase is not configured"))
        }
        
        if (!isNetworkAvailable()) {
            return@withContext Result.failure(Exception("No network connection available"))
        }
        
        return@withContext executeWithRetry {
            try {
                val response = supabaseConfig.client
                    .from(SupabaseConfig.SMS_MESSAGES_TABLE)
                    .select(columns = Columns.ALL)
                    .gt("timestamp", timestamp)
                    .order("timestamp", ascending = false)
                    .decodeList<SmsMessageDto>()
                
                val messages = response.map { it.toDomain() }
                Log.d(TAG, "Retrieved ${messages.size} SMS messages after timestamp $timestamp")
                
                Result.success(messages)
            } catch (e: Exception) {
                Log.e(TAG, "Error retrieving SMS messages after timestamp", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Update SMS message sync status
     */
    suspend fun updateSyncStatus(messageId: String, status: SyncStatus): Result<Unit> = withContext(Dispatchers.IO) {
        if (!supabaseConfig.isConfigured()) {
            return@withContext Result.failure(Exception("Supabase is not configured"))
        }
        
        return@withContext executeWithRetry {
            try {
                supabaseConfig.client
                    .from(SupabaseConfig.SMS_MESSAGES_TABLE)
                    .update(mapOf("sync_status" to status.name))
                    .eq("id", messageId)
                
                Log.d(TAG, "Updated sync status for message $messageId to $status")
                Result.success(Unit)
            } catch (e: Exception) {
                Log.e(TAG, "Error updating sync status", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * Execute operation with retry logic
     */
    private suspend fun <T> executeWithRetry(operation: suspend () -> Result<T>): Result<T> {
        repeat(MAX_RETRY_ATTEMPTS) { attempt ->
            val result = operation()
            if (result.isSuccess) {
                return result
            }
            
            if (attempt < MAX_RETRY_ATTEMPTS - 1) {
                Log.w(TAG, "Operation failed, retrying in ${RETRY_DELAY_MS}ms (attempt ${attempt + 1}/$MAX_RETRY_ATTEMPTS)")
                delay(RETRY_DELAY_MS * (attempt + 1)) // Exponential backoff
            }
        }
        
        return operation() // Final attempt
    }
}
