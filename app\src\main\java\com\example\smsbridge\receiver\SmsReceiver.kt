package com.example.smsbridge.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.provider.Telephony
import android.util.Log
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.workDataOf
import com.example.smsbridge.data.model.SmsMessage
import com.example.smsbridge.data.model.SmsType
import com.example.smsbridge.data.model.SyncStatus
import com.example.smsbridge.worker.SmsSyncWorker
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * BroadcastReceiver for intercepting incoming SMS messages
 */
@AndroidEntryPoint
class SmsReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "SmsReceiver"
    }
    
    @Inject
    lateinit var workManager: WorkManager
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "SMS received, action: ${intent.action}")
        
        when (intent.action) {
            Telephony.Sms.Intents.SMS_RECEIVED_ACTION -> {
                handleSmsReceived(context, intent)
            }
        }
    }
    
    /**
     * Handle incoming SMS messages
     */
    private fun handleSmsReceived(context: Context, intent: Intent) {
        try {
            val smsMessages = Telephony.Sms.Intents.getMessagesFromIntent(intent)
            
            if (smsMessages.isNullOrEmpty()) {
                Log.w(TAG, "No SMS messages found in intent")
                return
            }
            
            for (smsMessage in smsMessages) {
                val phoneNumber = smsMessage.originatingAddress ?: ""
                val messageBody = smsMessage.messageBody ?: ""
                val timestamp = smsMessage.timestampMillis
                
                Log.d(TAG, "Processing SMS from: $phoneNumber, body: ${messageBody.take(50)}...")
                
                // Create SmsMessage object
                val message = SmsMessage(
                    phoneNumber = phoneNumber,
                    content = messageBody,
                    timestamp = timestamp,
                    type = SmsType.INBOX,
                    isRead = false,
                    syncStatus = SyncStatus.PENDING
                )
                
                // Schedule sync work for this message
                scheduleSyncWork(context, message)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing received SMS", e)
        }
    }
    
    /**
     * Schedule background work to sync the SMS message
     */
    private fun scheduleSyncWork(context: Context, message: SmsMessage) {
        try {
            val workData = workDataOf(
                SmsSyncWorker.KEY_PHONE_NUMBER to message.phoneNumber,
                SmsSyncWorker.KEY_CONTENT to message.content,
                SmsSyncWorker.KEY_TIMESTAMP to message.timestamp,
                SmsSyncWorker.KEY_TYPE to message.type.name,
                SmsSyncWorker.KEY_IS_READ to message.isRead,
                SmsSyncWorker.KEY_SYNC_TYPE to SmsSyncWorker.SYNC_TYPE_REAL_TIME
            )
            
            val syncWorkRequest = OneTimeWorkRequestBuilder<SmsSyncWorker>()
                .setInputData(workData)
                .addTag(SmsSyncWorker.WORK_TAG_REAL_TIME_SYNC)
                .build()
            
            WorkManager.getInstance(context).enqueue(syncWorkRequest)
            
            Log.d(TAG, "Scheduled sync work for SMS from ${message.phoneNumber}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error scheduling sync work", e)
        }
    }
}
