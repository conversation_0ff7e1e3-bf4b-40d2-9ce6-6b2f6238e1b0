package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.smsbridge.SMSBridgeApplication",
    rootPackage = "com.example.smsbridge",
    originatingRoot = "com.example.smsbridge.SMSBridgeApplication",
    originatingRootPackage = "com.example.smsbridge",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "SMSBridgeApplication",
    originatingRootSimpleNames = "SMSBridgeApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_smsbridge_SMSBridgeApplication {
}
