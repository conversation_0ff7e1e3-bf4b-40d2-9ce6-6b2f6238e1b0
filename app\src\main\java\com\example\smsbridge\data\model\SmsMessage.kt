package com.example.smsbridge.data.model

import kotlinx.serialization.Serializable
import java.util.Date

/**
 * Data class representing an SMS message
 */
@Serializable
data class SmsMessage(
    val id: String? = null, // Supabase ID (null for new messages)
    val androidId: Long? = null, // Android SMS ID from ContentProvider
    val phoneNumber: String,
    val content: String,
    val timestamp: Long, // Unix timestamp in milliseconds
    val type: SmsType,
    val isRead: Boolean = false,
    val threadId: Long? = null,
    val deviceId: String? = null, // To identify which device sent the SMS
    val syncStatus: SyncStatus = SyncStatus.PENDING,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * SMS message types
 */
@Serializable
enum class SmsType(val value: Int) {
    INBOX(1),
    SENT(2),
    DRAFT(3),
    OUTBOX(4),
    FAILED(5),
    QUEUED(6);

    companion object {
        fun fromValue(value: Int): SmsType {
            return entries.find { it.value == value } ?: INBOX
        }
    }
}

/**
 * Sync status for tracking message synchronization
 */
@Serializable
enum class SyncStatus {
    PENDING,    // Not yet synced
    SYNCING,    // Currently being synced
    SYNCED,     // Successfully synced
    FAILED,     // Sync failed
    RETRY       // Waiting for retry
}

/**
 * Data class for Supabase database operations
 */
@Serializable
data class SmsMessageDto(
    val id: String? = null,
    val android_id: Long? = null,
    val phone_number: String,
    val content: String,
    val timestamp: Long,
    val type: String,
    val is_read: Boolean = false,
    val thread_id: Long? = null,
    val device_id: String? = null,
    val sync_status: String = SyncStatus.PENDING.name,
    val created_at: String? = null,
    val updated_at: String? = null
)

/**
 * Extension functions for converting between domain model and DTO
 */
fun SmsMessage.toDto(): SmsMessageDto {
    return SmsMessageDto(
        id = id,
        android_id = androidId,
        phone_number = phoneNumber,
        content = content,
        timestamp = timestamp,
        type = type.name,
        is_read = isRead,
        thread_id = threadId,
        device_id = deviceId,
        sync_status = syncStatus.name
    )
}

fun SmsMessageDto.toDomain(): SmsMessage {
    return SmsMessage(
        id = id,
        androidId = android_id,
        phoneNumber = phone_number,
        content = content,
        timestamp = timestamp,
        type = SmsType.valueOf(type),
        isRead = is_read,
        threadId = thread_id,
        deviceId = device_id,
        syncStatus = SyncStatus.valueOf(sync_status)
    )
}

/**
 * Data class for sync statistics
 */
@Serializable
data class SyncStats(
    val totalMessages: Int = 0,
    val syncedMessages: Int = 0,
    val pendingMessages: Int = 0,
    val failedMessages: Int = 0,
    val lastSyncTime: Long? = null,
    val isCurrentlySyncing: Boolean = false
)
