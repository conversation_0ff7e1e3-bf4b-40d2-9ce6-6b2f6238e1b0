package com.example.smsbridge.ui.screen.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel,
    onNavigateBack: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Settings") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                SupabaseConfigurationCard(
                    supabaseUrl = uiState.supabaseUrl,
                    supabaseAnonKey = uiState.supabaseAnonKey,
                    onUrlChange = viewModel::updateSupabaseUrl,
                    onKeyChange = viewModel::updateSupabaseAnonKey,
                    onSave = viewModel::saveSupabaseConfig,
                    isConfigured = uiState.isSupabaseConfigured
                )
            }
            
            item {
                SyncSettingsCard(
                    isAutoSyncEnabled = uiState.isAutoSyncEnabled,
                    isRealTimeSyncEnabled = uiState.isRealTimeSyncEnabled,
                    isSyncOnWifiOnly = uiState.isSyncOnWifiOnly,
                    syncFrequencyHours = uiState.syncFrequencyHours,
                    onAutoSyncToggle = viewModel::toggleAutoSync,
                    onRealTimeSyncToggle = viewModel::toggleRealTimeSync,
                    onWifiOnlyToggle = viewModel::toggleSyncOnWifiOnly,
                    onSyncFrequencyChange = viewModel::updateSyncFrequency
                )
            }
            
            item {
                DeviceInfoCard(
                    deviceId = uiState.deviceId
                )
            }
            
            if (uiState.message != null) {
                item {
                    MessageCard(
                        message = uiState.message,
                        isError = uiState.isError,
                        onDismiss = viewModel::clearMessage
                    )
                }
            }
        }
    }
}

@Composable
private fun SupabaseConfigurationCard(
    supabaseUrl: String,
    supabaseAnonKey: String,
    onUrlChange: (String) -> Unit,
    onKeyChange: (String) -> Unit,
    onSave: () -> Unit,
    isConfigured: Boolean
) {
    var showKey by remember { mutableStateOf(false) }
    
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Supabase Configuration",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                if (isConfigured) {
                    Icon(
                        imageVector = Icons.Default.Visibility,
                        contentDescription = "Configured",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            OutlinedTextField(
                value = supabaseUrl,
                onValueChange = onUrlChange,
                label = { Text("Supabase URL") },
                placeholder = { Text("https://your-project.supabase.co") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Uri)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            OutlinedTextField(
                value = supabaseAnonKey,
                onValueChange = onKeyChange,
                label = { Text("Supabase Anon Key") },
                placeholder = { Text("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                visualTransformation = if (showKey) {
                    VisualTransformation.None
                } else {
                    PasswordVisualTransformation()
                },
                trailingIcon = {
                    IconButton(onClick = { showKey = !showKey }) {
                        Icon(
                            imageVector = if (showKey) {
                                Icons.Default.VisibilityOff
                            } else {
                                Icons.Default.Visibility
                            },
                            contentDescription = if (showKey) "Hide key" else "Show key"
                        )
                    }
                }
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Button(
                onClick = onSave,
                modifier = Modifier.fillMaxWidth(),
                enabled = supabaseUrl.isNotBlank() && supabaseAnonKey.isNotBlank()
            ) {
                Text("Save Configuration")
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Get your Supabase URL and anon key from your Supabase project dashboard.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun SyncSettingsCard(
    isAutoSyncEnabled: Boolean,
    isRealTimeSyncEnabled: Boolean,
    isSyncOnWifiOnly: Boolean,
    syncFrequencyHours: Long,
    onAutoSyncToggle: (Boolean) -> Unit,
    onRealTimeSyncToggle: (Boolean) -> Unit,
    onWifiOnlyToggle: (Boolean) -> Unit,
    onSyncFrequencyChange: (Long) -> Unit
) {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Sync Settings",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            SettingItem(
                title = "Auto Sync",
                description = "Automatically sync SMS messages in the background",
                checked = isAutoSyncEnabled,
                onCheckedChange = onAutoSyncToggle
            )
            
            SettingItem(
                title = "Real-time Sync",
                description = "Sync new SMS messages immediately when received",
                checked = isRealTimeSyncEnabled,
                onCheckedChange = onRealTimeSyncToggle,
                enabled = isAutoSyncEnabled
            )
            
            SettingItem(
                title = "WiFi Only",
                description = "Only sync when connected to WiFi",
                checked = isSyncOnWifiOnly,
                onCheckedChange = onWifiOnlyToggle,
                enabled = isAutoSyncEnabled
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Sync Frequency: Every $syncFrequencyHours hours",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Slider(
                value = syncFrequencyHours.toFloat(),
                onValueChange = { onSyncFrequencyChange(it.toLong()) },
                valueRange = 1f..72f,
                steps = 23,
                enabled = isAutoSyncEnabled,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun SettingItem(
    title: String,
    description: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    enabled: Boolean = true
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                color = if (enabled) {
                    MaterialTheme.colorScheme.onSurface
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            enabled = enabled
        )
    }
}

@Composable
private fun DeviceInfoCard(
    deviceId: String?
) {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Device Information",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Device ID: ${deviceId ?: "Not set"}",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "This ID helps identify your device in the synced messages.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun MessageCard(
    message: String,
    isError: Boolean,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isError) {
                MaterialTheme.colorScheme.errorContainer
            } else {
                MaterialTheme.colorScheme.primaryContainer
            }
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = if (isError) {
                    MaterialTheme.colorScheme.onErrorContainer
                } else {
                    MaterialTheme.colorScheme.onPrimaryContainer
                }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            TextButton(onClick = onDismiss) {
                Text("Dismiss")
            }
        }
    }
}
