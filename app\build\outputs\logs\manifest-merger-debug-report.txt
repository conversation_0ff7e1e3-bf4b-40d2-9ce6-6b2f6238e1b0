-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:58:9-66:20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\041c913b04a56962d8a5d441a0eb19a0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\041c913b04a56962d8a5d441a0eb19a0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:62:13-31
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:60:13-68
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:61:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:59:13-67
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:2:1-70:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:2:1-70:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f65c66558cc6d096526f6faff00dc9e\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd773884fc6158e38de3b1f8b8e10483\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac618beab86753b6fb93ecfbfd6949f1\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f3b40ac08aca7ec7ef460df10d7dbca\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4eaf21a5f7540a07df1b3967c1f3d09\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b8cf9d816b4bd08abf78ab1bf1742cd\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\95be2e78697e7bcd488a9e6a0d025be4\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f537cd72a11debb129543fb4d2b44b\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7c18e2580e8cc70f8d62618789cedcb\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a21bb1b3d3b460bcffdeb4db9338bfb\transformed\realtime-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1fa05d6b105c99af2d5d7fca36ec017\transformed\postgrest-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aebc06674aa17edab915e6986dccfac\transformed\supabase-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e35bd8de798a3fec06a2816005c8114\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a1462cc8605c2609c0bf4ab08c6deb1\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4b01e9cd3fa56adc9989cf64ce3346f\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\569a4848e6cd56af57d5651d94321d08\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35018ac0925c576d257d08d0f22c558\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\deb758cdd33538441266bf499f5dc3f0\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a66074d77b36bc85e223e1c4ed5168\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12224fe584c6a32717904f03178b3116\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b69d03cdbe9b2b25d49d09e8a5debc\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa4bea1c9cac9beb0a1e8fddb70b6247\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8889440980a17a5e88a63467039bea7e\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45b99c9bf7aa7fb2d3bdef1a9444225d\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42b4fc9cc354693548278aceb3117702\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5f04198540afe3e4dc1313e3bcfb32\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\558cf4b1102d84e2513b5975a52852b5\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbbb6c859f6cbc42810a25628a5ef6b9\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9881bd69f50d320d31806a354955f54\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\711b4bb3e01ef9da68fe9bd783692769\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0eb922676c99d77089f9e796b49236d\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\021c922a74586cc13a58371cbd9e950c\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01073f4e3ce91005a588204341f2c60e\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d87e64a6c82e56857cca7aa1bb569a0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd357763fc5d12aea42dedd83b4a8eb5\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10784b9f858f168c721dd6d5cea2e8e1\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fefc12f5eb90e7fd801fba39aaa50f2d\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23c2a5d8129e29e2bc7a7a1f34e6aa6a\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cfdaa89b518545bf76d572ffec69ca8\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67e86e852064bb3ad15badc6d1beac16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c241fe8bc39be0739d96323ae1d70a51\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9077a541dbc7af6444802377ae9e2c64\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\293e0a93b583d12b03a491c963eb3e93\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\541de9f7a890346346177580997c6295\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\999def391aa6484bb48a56dfd56f9ef8\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ac125ed70ce4b086ace91f075b3157\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\23d96d8dc30edc1327a6b6700e70b904\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1924d4f727be9683a508cf7378675631\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\be43d6172353f7513543206d2cbe432a\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4892019ade97738c2d0518bb54470363\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef72e12342337972c57f6822ebd04b8\transformed\lifecycle-service-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4892c09ace4e456e4e2a065e96c679b3\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c632b2f8f0f70a31b9cb1e7c34c8f8\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\51dfddca8ba638c094fe1182d81b2b0c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\633a39a9b9a3ac22f07ac3578025771c\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a6efcd6e944944f46cc97a4ab22ec90\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\742db9bf6e4cc2a3647c38d1cffdd118\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da488a8ffec6746e9fc247368233399a\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f3b9103ba5813c87c6d88d003a932a3\transformed\room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c44c0b713c87c60a00e87a06f2ffcd2\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad925207df37b84adb52f282303a2d0b\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:17:1-35:12
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19b5a62ae266825600804d30b87c8800\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e38699a377b648ce8cbdbc16589f68f5\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0d26cc4467c0a2706204088d6e77115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2b88fbc6ae8dfdb54816ce0f2339ee5\transformed\krypto-debug\AndroidManifest.xml:2:1-9:12
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\76db654df3021c020d514a0493b55524\transformed\kermit-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ccdb799b3f27c8df01e77f90b4c99a32\transformed\multiplatform-settings-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\dcd4b5e1bfc1bdde747c36dba4f2c626\transformed\kermit-core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\041c913b04a56962d8a5d441a0eb19a0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dbbf0da420474fff1eed67be9095a58\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5bd68f5abeb31f9932189a12dbb60f\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c01120ed864c7e434cd04a70295ec04\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d7e3743ea8258efd33bfe8ca9907a1\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_SMS
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:7:5-70
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:7:22-67
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:8:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:8:22-72
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:11:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:11:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:12:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:12:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:15:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:15:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:16:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:16:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:17:5-87
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:17:22-84
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:19:5-68:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:19:5-68:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0d26cc4467c0a2706204088d6e77115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0d26cc4467c0a2706204088d6e77115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\041c913b04a56962d8a5d441a0eb19a0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\041c913b04a56962d8a5d441a0eb19a0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:27:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:25:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:23:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:26:9-54
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:24:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:21:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:28:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:22:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:20:9-45
activity#com.example.smsbridge.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:29:9-38:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:32:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:31:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:33:13-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:30:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:34:13-37:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:35:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:35:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:36:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:36:27-74
receiver#com.example.smsbridge.receiver.SmsReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:41:9-48:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:43:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:44:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:42:13-49
intent-filter#action:name:android.provider.Telephony.SMS_RECEIVED
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:45:13-47:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:45:28-51
action#android.provider.Telephony.SMS_RECEIVED
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:46:17-82
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:46:25-79
service#com.example.smsbridge.service.SmsSyncService
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:51:9-55:56
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:53:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:54:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:55:13-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:52:13-51
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:63:13-65:52
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:65:17-49
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml:64:17-68
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f65c66558cc6d096526f6faff00dc9e\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f65c66558cc6d096526f6faff00dc9e\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd773884fc6158e38de3b1f8b8e10483\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd773884fc6158e38de3b1f8b8e10483\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac618beab86753b6fb93ecfbfd6949f1\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac618beab86753b6fb93ecfbfd6949f1\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f3b40ac08aca7ec7ef460df10d7dbca\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f3b40ac08aca7ec7ef460df10d7dbca\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4eaf21a5f7540a07df1b3967c1f3d09\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4eaf21a5f7540a07df1b3967c1f3d09\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b8cf9d816b4bd08abf78ab1bf1742cd\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b8cf9d816b4bd08abf78ab1bf1742cd\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\95be2e78697e7bcd488a9e6a0d025be4\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\95be2e78697e7bcd488a9e6a0d025be4\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f537cd72a11debb129543fb4d2b44b\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1f537cd72a11debb129543fb4d2b44b\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7c18e2580e8cc70f8d62618789cedcb\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7c18e2580e8cc70f8d62618789cedcb\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a21bb1b3d3b460bcffdeb4db9338bfb\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a21bb1b3d3b460bcffdeb4db9338bfb\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1fa05d6b105c99af2d5d7fca36ec017\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1fa05d6b105c99af2d5d7fca36ec017\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aebc06674aa17edab915e6986dccfac\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2aebc06674aa17edab915e6986dccfac\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e35bd8de798a3fec06a2816005c8114\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e35bd8de798a3fec06a2816005c8114\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a1462cc8605c2609c0bf4ab08c6deb1\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a1462cc8605c2609c0bf4ab08c6deb1\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4b01e9cd3fa56adc9989cf64ce3346f\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4b01e9cd3fa56adc9989cf64ce3346f\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\569a4848e6cd56af57d5651d94321d08\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\569a4848e6cd56af57d5651d94321d08\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35018ac0925c576d257d08d0f22c558\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d35018ac0925c576d257d08d0f22c558\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\deb758cdd33538441266bf499f5dc3f0\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\deb758cdd33538441266bf499f5dc3f0\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a66074d77b36bc85e223e1c4ed5168\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\79a66074d77b36bc85e223e1c4ed5168\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12224fe584c6a32717904f03178b3116\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12224fe584c6a32717904f03178b3116\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b69d03cdbe9b2b25d49d09e8a5debc\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b69d03cdbe9b2b25d49d09e8a5debc\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa4bea1c9cac9beb0a1e8fddb70b6247\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa4bea1c9cac9beb0a1e8fddb70b6247\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8889440980a17a5e88a63467039bea7e\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8889440980a17a5e88a63467039bea7e\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45b99c9bf7aa7fb2d3bdef1a9444225d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45b99c9bf7aa7fb2d3bdef1a9444225d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42b4fc9cc354693548278aceb3117702\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42b4fc9cc354693548278aceb3117702\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5f04198540afe3e4dc1313e3bcfb32\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de5f04198540afe3e4dc1313e3bcfb32\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\558cf4b1102d84e2513b5975a52852b5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\558cf4b1102d84e2513b5975a52852b5\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbbb6c859f6cbc42810a25628a5ef6b9\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbbb6c859f6cbc42810a25628a5ef6b9\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9881bd69f50d320d31806a354955f54\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9881bd69f50d320d31806a354955f54\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\711b4bb3e01ef9da68fe9bd783692769\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\711b4bb3e01ef9da68fe9bd783692769\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0eb922676c99d77089f9e796b49236d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0eb922676c99d77089f9e796b49236d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\021c922a74586cc13a58371cbd9e950c\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\021c922a74586cc13a58371cbd9e950c\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01073f4e3ce91005a588204341f2c60e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01073f4e3ce91005a588204341f2c60e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d87e64a6c82e56857cca7aa1bb569a0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d87e64a6c82e56857cca7aa1bb569a0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd357763fc5d12aea42dedd83b4a8eb5\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd357763fc5d12aea42dedd83b4a8eb5\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10784b9f858f168c721dd6d5cea2e8e1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10784b9f858f168c721dd6d5cea2e8e1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fefc12f5eb90e7fd801fba39aaa50f2d\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fefc12f5eb90e7fd801fba39aaa50f2d\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23c2a5d8129e29e2bc7a7a1f34e6aa6a\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23c2a5d8129e29e2bc7a7a1f34e6aa6a\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cfdaa89b518545bf76d572ffec69ca8\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cfdaa89b518545bf76d572ffec69ca8\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67e86e852064bb3ad15badc6d1beac16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67e86e852064bb3ad15badc6d1beac16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c241fe8bc39be0739d96323ae1d70a51\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c241fe8bc39be0739d96323ae1d70a51\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9077a541dbc7af6444802377ae9e2c64\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9077a541dbc7af6444802377ae9e2c64\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\293e0a93b583d12b03a491c963eb3e93\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\293e0a93b583d12b03a491c963eb3e93\transformed\lifecycle-viewmodel-2.8.4\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\541de9f7a890346346177580997c6295\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\541de9f7a890346346177580997c6295\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\999def391aa6484bb48a56dfd56f9ef8\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\999def391aa6484bb48a56dfd56f9ef8\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ac125ed70ce4b086ace91f075b3157\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ac125ed70ce4b086ace91f075b3157\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\23d96d8dc30edc1327a6b6700e70b904\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\23d96d8dc30edc1327a6b6700e70b904\transformed\lifecycle-livedata-core-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1924d4f727be9683a508cf7378675631\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1924d4f727be9683a508cf7378675631\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\be43d6172353f7513543206d2cbe432a\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\be43d6172353f7513543206d2cbe432a\transformed\lifecycle-viewmodel-savedstate-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4892019ade97738c2d0518bb54470363\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4892019ade97738c2d0518bb54470363\transformed\lifecycle-viewmodel-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef72e12342337972c57f6822ebd04b8\transformed\lifecycle-service-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef72e12342337972c57f6822ebd04b8\transformed\lifecycle-service-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4892c09ace4e456e4e2a065e96c679b3\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4892c09ace4e456e4e2a065e96c679b3\transformed\lifecycle-livedata-core-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c632b2f8f0f70a31b9cb1e7c34c8f8\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c632b2f8f0f70a31b9cb1e7c34c8f8\transformed\lifecycle-livedata-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\51dfddca8ba638c094fe1182d81b2b0c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\51dfddca8ba638c094fe1182d81b2b0c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\633a39a9b9a3ac22f07ac3578025771c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\633a39a9b9a3ac22f07ac3578025771c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a6efcd6e944944f46cc97a4ab22ec90\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a6efcd6e944944f46cc97a4ab22ec90\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\742db9bf6e4cc2a3647c38d1cffdd118\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\742db9bf6e4cc2a3647c38d1cffdd118\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da488a8ffec6746e9fc247368233399a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da488a8ffec6746e9fc247368233399a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f3b9103ba5813c87c6d88d003a932a3\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f3b9103ba5813c87c6d88d003a932a3\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c44c0b713c87c60a00e87a06f2ffcd2\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c44c0b713c87c60a00e87a06f2ffcd2\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad925207df37b84adb52f282303a2d0b\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad925207df37b84adb52f282303a2d0b\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19b5a62ae266825600804d30b87c8800\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19b5a62ae266825600804d30b87c8800\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e38699a377b648ce8cbdbc16589f68f5\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e38699a377b648ce8cbdbc16589f68f5\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0d26cc4467c0a2706204088d6e77115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0d26cc4467c0a2706204088d6e77115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2b88fbc6ae8dfdb54816ce0f2339ee5\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2b88fbc6ae8dfdb54816ce0f2339ee5\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\76db654df3021c020d514a0493b55524\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\76db654df3021c020d514a0493b55524\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ccdb799b3f27c8df01e77f90b4c99a32\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ccdb799b3f27c8df01e77f90b4c99a32\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\dcd4b5e1bfc1bdde747c36dba4f2c626\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\dcd4b5e1bfc1bdde747c36dba4f2c626\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\041c913b04a56962d8a5d441a0eb19a0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\041c913b04a56962d8a5d441a0eb19a0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dbbf0da420474fff1eed67be9095a58\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dbbf0da420474fff1eed67be9095a58\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5bd68f5abeb31f9932189a12dbb60f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b5bd68f5abeb31f9932189a12dbb60f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c01120ed864c7e434cd04a70295ec04\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c01120ed864c7e434cd04a70295ec04\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d7e3743ea8258efd33bfe8ca9907a1\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7d7e3743ea8258efd33bfe8ca9907a1\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\SMSBridge\app\src\main\AndroidManifest.xml
meta-data#io.github.jan.supabase.gotrue.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f34a5060d0c6d39b2bccc4d7df707f9\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00285e1df03d11f6a2bc73bd2b29fc52\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\182776f05d04217b3c00210d5d3f5195\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021718821513520999bbfdf603ab9fc3\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fd5e23ba2620bbe5956da79d2261ca9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.smsbridge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.smsbridge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f116294e462fcef23dad22a63f303a87\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ec1a9e5d4865c3c75bc35f38b3eea24\transformed\lifecycle-process-2.8.4\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511687d8ccf91ee57bd804551e40d10\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#com.russhwolf.settings.SettingsInitializer
ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98210228769f829ca7ed66a3d798899e\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a21c8f5375dd77972a869ff23b00f41\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
