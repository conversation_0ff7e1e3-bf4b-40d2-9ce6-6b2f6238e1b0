package com.example.smsbridge.ui.screen.permissions;

import com.example.smsbridge.data.preferences.PreferencesManager;
import com.example.smsbridge.util.PermissionManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PermissionsViewModel_Factory implements Factory<PermissionsViewModel> {
  private final Provider<PermissionManager> permissionManagerProvider;

  private final Provider<PreferencesManager> preferencesManagerProvider;

  public PermissionsViewModel_Factory(Provider<PermissionManager> permissionManagerProvider,
      Provider<PreferencesManager> preferencesManagerProvider) {
    this.permissionManagerProvider = permissionManagerProvider;
    this.preferencesManagerProvider = preferencesManagerProvider;
  }

  @Override
  public PermissionsViewModel get() {
    return newInstance(permissionManagerProvider.get(), preferencesManagerProvider.get());
  }

  public static PermissionsViewModel_Factory create(
      Provider<PermissionManager> permissionManagerProvider,
      Provider<PreferencesManager> preferencesManagerProvider) {
    return new PermissionsViewModel_Factory(permissionManagerProvider, preferencesManagerProvider);
  }

  public static PermissionsViewModel newInstance(PermissionManager permissionManager,
      PreferencesManager preferencesManager) {
    return new PermissionsViewModel(permissionManager, preferencesManager);
  }
}
